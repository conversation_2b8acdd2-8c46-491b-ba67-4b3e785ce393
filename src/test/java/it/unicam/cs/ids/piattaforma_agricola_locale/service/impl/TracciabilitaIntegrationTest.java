// package it.unicam.cs.ids.piattaforma_agricola_locale.service.impl;

// import it.unicam.cs.ids.piattaforma_agricola_locale.dto.tracciabilita.TracciabilitaProdottoDTO;
// import it.unicam.cs.ids.piattaforma_agricola_locale.dto.tracciabilita.TracciabilitaProcessoDTO;
// import it.unicam.cs.ids.piattaforma_agricola_locale.dto.tracciabilita.TracciabilitaFaseDTO;
// import it.unicam.cs.ids.piattaforma_agricola_locale.dto.tracciabilita.TracciabilitaInputDTO;
// import it.unicam.cs.ids.piattaforma_agricola_locale.dto.tracciabilita.ProduttoreMateriaPrimaDTO;
// import it.unicam.cs.ids.piattaforma_agricola_locale.model.catalogo.Prodotto;
// import it.unicam.cs.ids.piattaforma_agricola_locale.model.catalogo.TipoOrigineProdotto;
// import it.unicam.cs.ids.piattaforma_agricola_locale.model.repository.ProdottoRepository;
// import it.unicam.cs.ids.piattaforma_agricola_locale.model.repository.ProcessoTrasformazioneRepository;
// import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.DatiAzienda;
// import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.Trasformatore;
// import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.TipoRuolo;
// import it.unicam.cs.ids.piattaforma_agricola_locale.model.trasformazione.ProcessoTrasformazione;
// import it.unicam.cs.ids.piattaforma_agricola_locale.model.trasformazione.FaseLavorazione;
// import it.unicam.cs.ids.piattaforma_agricola_locale.model.trasformazione.InputLavorazione;
// import org.junit.jupiter.api.BeforeEach;
// import org.junit.jupiter.api.Test;
// import org.junit.jupiter.api.DisplayName;
// import org.junit.jupiter.api.Nested;

// import java.time.LocalDateTime;
// import java.util.List;
// import java.util.Arrays;
// import java.util.ArrayList;

// import static org.junit.jupiter.api.Assertions.*;

// /**
//  * Test di integrazione completo per il sistema di tracciabilità con scenari
//  * multi-livello
//  * di trasformazione. Verifica la corretta implementazione della logica
//  * ricorsiva
//  * per determinare il produttore originario e la coerenza dei DTO di
//  * tracciabilità.
//  * 
//  * Scenari testati:
//  * 1. Materia prima semplice (COLTIVATO/ALLEVATO)
//  * 2. Trasformazione a un livello (Materia Prima → Prodotto Trasformato)
//  * 3. Trasformazione multi-livello (MP → Trasformato1 → Trasformato2 → Prodotto
//  * Finale)
//  * 4. Caso edge: cicli nella catena di trasformazione
//  * 5. Caso edge: prodotto senza processo di trasformazione
//  */
// @DisplayName("Test di Integrazione - Sistema di Tracciabilità Prodotti Trasformati")
// public class TracciabilitaIntegrationTest {

//     private ProdottoService prodottoService;
//     private ProcessoTrasformazioneService processoService;
//     private ProdottoRepository prodottoRepository;
//     private ProcessoTrasformazioneRepository processoRepository;

//     // Dati di test per venditori/produttori
//     private Trasformatore produttoreMateriePrime;
//     private Trasformatore trasformatoreLivello1;
//     private Trasformatore trasformatoreLivello2;

//     // Prodotti di test
//     private Prodotto pomodoroMateriaPrima;
//     private Prodotto latteMateriaPrima;
//     private Prodotto passataPomodoro;
//     private Prodotto formaggioStagionato;
//     private Prodotto salsaSpeciale; // Prodotto finale multi-livello

//     @BeforeEach
//     void setUp() {
//         // Inizializzazione repositories e servizi
//         prodottoRepository = new ProdottoRepository();
//         processoRepository = new ProcessoTrasformazioneRepository();
//         processoService = new ProcessoTrasformazioneService(processoRepository);
//         prodottoService = new ProdottoService(prodottoRepository, null, null, processoService);

//         // Setup produttori/venditori
//         setupProduttoriVenditori();

//         // Setup prodotti
//         setupProdotti();

//         // Setup processi di trasformazione
//         setupProcessiTrasformazione();
//     }

//     /**
//      * Setup dei produttori e venditori per i test
//      */
//     private void setupProduttoriVenditori() {
//         // Produttore materie prime (origini COLTIVATO/ALLEVATO)
//         DatiAzienda aziendaAgricola = new DatiAzienda(1, "Azienda Agricola Siciliana",
//                 "12345678901", "Via Roma 123, Palermo", "Azienda agricola tradizionale",
//                 "<EMAIL>", "091123456");
//         produttoreMateriePrime = new Trasformatore(
//                 1, "Giuseppe", "Verde",
//                 "<EMAIL>", "password123", "3331234567",
//                 aziendaAgricola,  TipoRuolo.TRASFORMATORE);

//         // Trasformatore primo livello
//         DatiAzienda aziendaTrasformazione1 = new DatiAzienda(2,
//                 "Conservificio Mediterraneo",
//                 "98765432109",
//                 "Via Napoli 45, Napoli", "Conservificio tradizionale",
//                 "<EMAIL>", "081987654");
//         trasformatoreLivello1 = new Trasformatore(
//                 2, "Marco", "Rossi",
//                 "<EMAIL>", "password456", "3339876543",
//                 aziendaTrasformazione1,  TipoRuolo.TRASFORMATORE);

//         // Trasformatore secondo livello
//         DatiAzienda aziendaTrasformazione2 = new DatiAzienda(3,
//                 "Gastronomia Artigianale",
//                 "11223344556",
//                 "Via Milano 78, Milano", "Gastronomia di alta qualità",
//                 "<EMAIL>", "02555666");
//         trasformatoreLivello2 = new Trasformatore(
//                 3, "Luigi", "Bianchi",
//                 "<EMAIL>", "password789", "3337894561",
//                 aziendaTrasformazione2,  TipoRuolo.TRASFORMATORE);
//     }

//     /**
//      * Setup dei prodotti per scenari di test complessi
//      */
//     private void setupProdotti() {
//         // Materie prime (origine COLTIVATO/ALLEVATO)
//         pomodoroMateriaPrima = new Prodotto(
//                 101, "Pomodori San Marzano DOP",
//                 "Pomodori biologici coltivati in Campania",
//                 3.50, 1000, produttoreMateriePrime);
//         pomodoroMateriaPrima.setTipoOrigine(TipoOrigineProdotto.COLTIVATO_ALLEVATO);

//         latteMateriaPrima = new Prodotto(
//                 102, "Latte Fresco Biologico",
//                 "Latte fresco da allevamento biologico",
//                 2.80, 500, produttoreMateriePrime);
//         latteMateriaPrima.setTipoOrigine(TipoOrigineProdotto.COLTIVATO_ALLEVATO);

//         // Prodotti trasformati livello 1
//         passataPomodoro = new Prodotto(
//                 201, "Passata di Pomodoro DOP",
//                 "Passata di pomodoro San Marzano lavorata artigianalmente",
//                 6.50, 200, trasformatoreLivello1);
//         passataPomodoro.setTipoOrigine(TipoOrigineProdotto.TRASFORMATO);

//         formaggioStagionato = new Prodotto(
//                 202, "Formaggio Stagionato",
//                 "Formaggio a pasta dura stagionato 12 mesi",
//                 15.00, 50, trasformatoreLivello1);
//         formaggioStagionato.setTipoOrigine(TipoOrigineProdotto.TRASFORMATO);

//         // Prodotto trasformato multi-livello (livello 2)
//         salsaSpeciale = new Prodotto(
//                 301, "Salsa Gourmet Speciale",
//                 "Salsa gourmet con passata di pomodoro e formaggio stagionato",
//                 25.00, 30, trasformatoreLivello2);
//         salsaSpeciale.setTipoOrigine(TipoOrigineProdotto.TRASFORMATO);

//         // Salva i prodotti nel repository
//         prodottoRepository.save(pomodoroMateriaPrima);
//         prodottoRepository.save(latteMateriaPrima);
//         prodottoRepository.save(passataPomodoro);
//         prodottoRepository.save(formaggioStagionato);
//         prodottoRepository.save(salsaSpeciale);
//     }

//     /**
//      * Setup dei processi di trasformazione multi-livello
//      */
//     private void setupProcessiTrasformazione() {
//         LocalDateTime oggi = LocalDateTime.now();
//         LocalDateTime ieri = oggi.minusDays(1);
//         LocalDateTime dueGiorniFa = oggi.minusDays(2);

//         // Processo 1: Pomodori → Passata di Pomodoro
//         ProcessoTrasformazione processoPassata = new ProcessoTrasformazione(
//                 "Produzione Passata DOP", "Lavorazione artigianale pomodori", trasformatoreLivello1);
//         processoPassata.setId(1001L); // Assegna ID al processo

//         // Setup fasi lavorazione
//         FaseLavorazione lavaggio = new FaseLavorazione(
//                 "Lavaggio e Selezione", "Lavaggio accurato e selezione pomodori maturi", 1, 30, true);
//         lavaggio.setStrumentiUtilizzati("Lavatrici industriali, tavoli di selezione");

//         FaseLavorazione cottura = new FaseLavorazione(
//                 "Cottura e Passatura", "Cottura a bassa temperatura e passatura", 2, 120, true);
//         cottura.setStrumentiUtilizzati("Caldaie a vapore, passaverdure industriali");

//         // Crea e aggiungi input alla fase di lavaggio
//         InputLavorazione inputPomodori = new InputLavorazione(
//                 pomodoroMateriaPrima, 500.0, "kg");
//         lavaggio.aggiungiInput(inputPomodori);

//         // Aggiungi fasi al processo
//         processoPassata.aggiungiFase(lavaggio);
//         processoPassata.aggiungiFase(cottura);

//         // Processo 2: Latte → Formaggio Stagionato
//         ProcessoTrasformazione processoFormaggio = new ProcessoTrasformazione(
//                 "Produzione Formaggio Stagionato", "Caseificazione tradizionale", trasformatoreLivello1);
//         processoFormaggio.setId(1002L); // Assegna ID al processo

//         // Setup fasi di lavorazione
//         FaseLavorazione caseificazione = new FaseLavorazione(
//                 "Caseificazione", "Coagulazione del latte e formazione cagliata", 1, 180, true);
//         caseificazione.setStrumentiUtilizzati("Caldaia per latte, caglio, fermenti lattici");

//         FaseLavorazione stagionatura = new FaseLavorazione(
//                 "Stagionatura", "Stagionatura in grotte naturali per 12 mesi", 2, 8760, true // 365 giorni * 24 ore
//         );
//         stagionatura.setStrumentiUtilizzati("Grotte di stagionatura, scaffalature in legno");

//         // Crea e aggiungi input alla fase di caseificazione
//         InputLavorazione inputLatte = new InputLavorazione(
//                 latteMateriaPrima, 100.0, "litri");
//         caseificazione.aggiungiInput(inputLatte);

//         // Aggiungi fasi al processo
//         processoFormaggio.aggiungiFase(caseificazione);
//         processoFormaggio.aggiungiFase(stagionatura);

//         // Processo 3: Passata + Formaggio → Salsa Speciale (Multi-livello)
//         ProcessoTrasformazione processoSalsa = new ProcessoTrasformazione(
//                 "Produzione Salsa Gourmet", "Creazione salsa con ingredienti selezionati", trasformatoreLivello2);
//         processoSalsa.setId(1003L); // Assegna ID al processo

//         // Setup fasi di lavorazione
//         FaseLavorazione miscelazione = new FaseLavorazione(
//                 "Miscelazione Ingredienti", "Miscelazione accurata passata e formaggio", 1, 45, true);
//         miscelazione.setStrumentiUtilizzati("Miscelatori industriali, dosatori automatici");

//         FaseLavorazione confezionamento = new FaseLavorazione(
//                 "Confezionamento", "Confezionamento sottovuoto in vasetti di vetro", 2, 60, true);
//         confezionamento.setStrumentiUtilizzati("Confezionatrici sottovuoto, sterilizzatori");

//         // Crea e aggiungi input alla fase di miscelazione
//         InputLavorazione inputPassata = new InputLavorazione(
//                 passataPomodoro, 10.0, "kg");
//         InputLavorazione inputFormaggio = new InputLavorazione(
//                 formaggioStagionato, 2.0, "kg");

//         miscelazione.aggiungiInput(inputPassata);
//         miscelazione.aggiungiInput(inputFormaggio);

//         // Aggiungi fasi al processo
//         processoSalsa.aggiungiFase(miscelazione);
//         processoSalsa.aggiungiFase(confezionamento);

//         // Salva i processi nel repository
//         processoRepository.save(processoPassata);
//         processoRepository.save(processoFormaggio);
//         processoRepository.save(processoSalsa);

//         // Associa i processi ai prodotti
//         passataPomodoro.setIdProcessoTrasformazioneOriginario(processoPassata.getId());
//         formaggioStagionato.setIdProcessoTrasformazioneOriginario(processoFormaggio.getId());
//         salsaSpeciale.setIdProcessoTrasformazioneOriginario(processoSalsa.getId());

//         // Aggiorniamo anche i prodotti nel repository dopo aver impostato l'ID del
//         // processo
//         prodottoRepository.save(passataPomodoro);
//         prodottoRepository.save(formaggioStagionato);
//         prodottoRepository.save(salsaSpeciale);
//     }

//     @Nested
//     @DisplayName("Test Scenari Semplici - Materie Prime")
//     class TestMateriePrime {

//         @Test
//         @DisplayName("Tracciabilità materia prima COLTIVATO - deve identificare produttore originario")
//         void testTracciabilitaMateriaPrimaColtivato() {
//             TracciabilitaProdottoDTO tracciabilita = prodottoService.getDettagliTracciabilita(101);

//             assertNotNull(tracciabilita, "La tracciabilità non dovrebbe essere null");
//             assertEquals("Pomodori San Marzano DOP", tracciabilita.getNomeProdotto());
//             assertEquals("Giuseppe Verde", tracciabilita.getNomeVenditoreCorrente());

//             // Verifica produttore originario
//             ProduttoreMateriaPrimaDTO produttoreOriginario = tracciabilita.getProduttoreOriginario();
//             assertNotNull(produttoreOriginario, "Il produttore originario dovrebbe essere identificato");
//             assertEquals("Giuseppe", produttoreOriginario.getNomeProduttore());
//             assertEquals("Verde", produttoreOriginario.getCognomeProduttore());
//             assertEquals("Azienda Agricola Siciliana", produttoreOriginario.getNomeAzienda());
//             assertEquals("COLTIVATO", produttoreOriginario.getTipoOrigine());

//             // Non dovrebbe avere processi di trasformazione
//             assertTrue(tracciabilita.getProcessiTrasformazione().isEmpty(),
//                     "Una materia prima non dovrebbe avere processi di trasformazione");
//         }

//         @Test
//         @DisplayName("Tracciabilità materia prima ALLEVATO - deve identificare produttore originario")
//         void testTracciabilitaMateriaPrimaAllevato() {
//             TracciabilitaProdottoDTO tracciabilita = prodottoService.getDettagliTracciabilita(102);

//             assertNotNull(tracciabilita);
//             assertEquals("Latte Fresco Biologico", tracciabilita.getNomeProdotto());

//             ProduttoreMateriaPrimaDTO produttoreOriginario = tracciabilita.getProduttoreOriginario();
//             assertNotNull(produttoreOriginario);
//             assertEquals("Giuseppe", produttoreOriginario.getNomeProduttore());
//             assertEquals("ALLEVATO", produttoreOriginario.getTipoOrigine());

//             assertTrue(tracciabilita.getProcessiTrasformazione().isEmpty());
//         }
//     }

//     @Nested
//     @DisplayName("Test Trasformazione Singolo Livello")
//     class TestTrasformazioneSingolaLivello {

//         @Test
//         @DisplayName("Tracciabilità prodotto trasformato - deve risalire al produttore originario")
//         void testTracciabilitaProdottoTrasformato() {
//             TracciabilitaProdottoDTO tracciabilita = prodottoService.getDettagliTracciabilita(201);

//             assertNotNull(tracciabilita);
//             assertEquals("Passata di Pomodoro DOP", tracciabilita.getNomeProdotto());
//             assertEquals("Marco Rossi", tracciabilita.getNomeVenditoreCorrente());
//             assertEquals("Conservificio Mediterraneo", tracciabilita.getAziendaVenditoreCorrente());

//             // Verifica che risalga al produttore originario attraverso la catena
//             ProduttoreMateriaPrimaDTO produttoreOriginario = tracciabilita.getProduttoreOriginario();
//             assertNotNull(produttoreOriginario, "Deve risalire al produttore originario della materia prima");
//             assertEquals("Giuseppe", produttoreOriginario.getNomeProduttore());
//             assertEquals("Verde", produttoreOriginario.getCognomeProduttore());
//             assertEquals("COLTIVATO", produttoreOriginario.getTipoOrigine());

//             // Verifica processo di trasformazione
//             assertFalse(tracciabilita.getProcessiTrasformazione().isEmpty(),
//                     "Dovrebbe avere almeno un processo di trasformazione");

//             TracciabilitaProcessoDTO processo = tracciabilita.getProcessiTrasformazione().get(0);
//             assertEquals("Produzione Passata DOP", processo.getNomeProcesso());
//             assertEquals("Marco", processo.getNomeResponsabile());
//             assertEquals("Rossi", processo.getCognomeResponsabile());

//             // Verifica fasi di lavorazione
//             assertEquals(2, processo.getFasi().size(), "Dovrebbe avere 2 fasi di lavorazione");

//             TracciabilitaFaseDTO primaFase = processo.getFasi().get(0);
//             assertEquals("Lavaggio e Selezione", primaFase.getNomeFase());
//             assertEquals(30, primaFase.getDurataMinuti());
//             assertEquals("Lavatrici industriali, tavoli di selezione", primaFase.getStrumentiUtilizzati());

//             TracciabilitaFaseDTO secondaFase = processo.getFasi().get(1);
//             assertEquals("Cottura e Passatura", secondaFase.getNomeFase());
//             assertEquals("Caldaie a vapore, passaverdure industriali", secondaFase.getStrumentiUtilizzati());

//             // Verifica input del processo
//             assertEquals(1, processo.getInput().size(), "Dovrebbe avere 1 input");
//             TracciabilitaInputDTO input = processo.getInput().get(0);
//             assertEquals("Pomodori San Marzano DOP", input.getNomeProdotto());
//             assertEquals(500.0, input.getQuantitaUtilizzata());
//         }

//         @Test
//         @DisplayName("Tracciabilità formaggio stagionato - processo lungo con produttore originario")
//         void testTracciabilitaFormaggioStagionato() {
//             TracciabilitaProdottoDTO tracciabilita = prodottoService.getDettagliTracciabilita(202);

//             assertNotNull(tracciabilita);
//             assertEquals("Formaggio Stagionato", tracciabilita.getNomeProdotto());

//             // Verifica produttore originario (allevatore)
//             ProduttoreMateriaPrimaDTO produttoreOriginario = tracciabilita.getProduttoreOriginario();
//             assertNotNull(produttoreOriginario);
//             assertEquals("ALLEVATO", produttoreOriginario.getTipoOrigine());
//             assertEquals("Giuseppe", produttoreOriginario.getNomeProduttore());

//             // Verifica processo con stagionatura lunga
//             TracciabilitaProcessoDTO processo = tracciabilita.getProcessiTrasformazione().get(0);
//             assertEquals("Produzione Formaggio Stagionato", processo.getNomeProcesso());

//             TracciabilitaFaseDTO faseStagionatura = processo.getFasi().stream()
//                     .filter(f -> f.getNomeFase().equals("Stagionatura"))
//                     .findFirst()
//                     .orElse(null);
//             assertNotNull(faseStagionatura, "Deve avere la fase di stagionatura");
//             assertEquals(8760, faseStagionatura.getDurataMinuti()); // 12 mesi
//             assertEquals("Grotte di stagionatura, scaffalature in legno", faseStagionatura.getStrumentiUtilizzati());
//         }
//     }

//     @Nested
//     @DisplayName("Test Trasformazione Multi-Livello - Scenario Complesso")
//     class TestTrasformazioneMultiLivello {

//         @Test
//         @DisplayName("Tracciabilità prodotto multi-livello - deve risalire attraverso catena complessa")
//         void testTracciabilitaProdottoMultiLivello() {
//             TracciabilitaProdottoDTO tracciabilita = prodottoService.getDettagliTracciabilita(301);

//             assertNotNull(tracciabilita);
//             assertEquals("Salsa Gourmet Speciale", tracciabilita.getNomeProdotto());
//             assertEquals("Luigi Bianchi", tracciabilita.getNomeVenditoreCorrente());
//             assertEquals("Gastronomia Artigianale", tracciabilita.getAziendaVenditoreCorrente());

//             // PUNTO CRITICO: Test della logica ricorsiva - deve risalire fino all'origine
//             ProduttoreMateriaPrimaDTO produttoreOriginario = tracciabilita.getProduttoreOriginario();
//             assertNotNull(produttoreOriginario,
//                     "La logica ricorsiva deve risalire fino al produttore originario attraverso catena multi-livello");
//             assertEquals("Giuseppe", produttoreOriginario.getNomeProduttore());
//             assertEquals("Verde", produttoreOriginario.getCognomeProduttore());
//             assertEquals("Azienda Agricola Siciliana", produttoreOriginario.getNomeAzienda());

//             // Verifica che identifichi la materia prima principale (COLTIVATO ha priorità
//             // su ALLEVATO)
//             assertEquals("COLTIVATO", produttoreOriginario.getTipoOrigine(),
//                     "Deve privilegiare COLTIVATO rispetto ad ALLEVATO nella strategia di priorità");

//             // Verifica presenza di processi di trasformazione
//             assertFalse(tracciabilita.getProcessiTrasformazione().isEmpty(),
//                     "Deve avere almeno il processo di trasformazione finale");

//             TracciabilitaProcessoDTO processoFinale = tracciabilita.getProcessiTrasformazione().get(0);
//             assertEquals("Produzione Salsa Gourmet", processoFinale.getNomeProcesso());
//             assertEquals("Luigi", processoFinale.getNomeResponsabile());

//             // Verifica input multi-livello del processo finale
//             assertEquals(2, processoFinale.getInput().size(),
//                     "Il processo finale dovrebbe avere 2 input (passata + formaggio)");

//             List<String> nomiInput = processoFinale.getInput().stream()
//                     .map(TracciabilitaInputDTO::getNomeProdotto)
//                     .sorted()
//                     .toList();

//             assertTrue(nomiInput.contains("Passata di Pomodoro DOP"),
//                     "Deve contenere la passata come input");
//             assertTrue(nomiInput.contains("Formaggio Stagionato"),
//                     "Deve contenere il formaggio come input");

//             // Verifica fasi del processo finale
//             assertEquals(2, processoFinale.getFasi().size());
//             assertTrue(processoFinale.getFasi().stream()
//                     .anyMatch(f -> f.getNomeFase().equals("Miscelazione Ingredienti")));
//             assertTrue(processoFinale.getFasi().stream()
//                     .anyMatch(f -> f.getNomeFase().equals("Confezionamento")));
//         }

//         @Test
//         @DisplayName("Verifica strategia priorità materie prime - COLTIVATO vs ALLEVATO")
//         void testStrategiaPrioritaMateriePrime() {
//             TracciabilitaProdottoDTO tracciabilita = prodottoService.getDettagliTracciabilita(301);

//             // La salsa contiene sia passata (da COLTIVATO) che formaggio (da ALLEVATO)
//             // La strategia dovrebbe privilegiare COLTIVATO
//             ProduttoreMateriaPrimaDTO produttoreOriginario = tracciabilita.getProduttoreOriginario();
//             assertNotNull(produttoreOriginario);
//             assertEquals("COLTIVATO", produttoreOriginario.getTipoOrigine(),
//                     "La strategia di priorità deve privilegiare COLTIVATO rispetto ad ALLEVATO");
//         }
//     }

//     @Nested
//     @DisplayName("Test Casi Edge e Gestione Errori")
//     class TestCasiEdge {

//         @Test
//         @DisplayName("Prodotto inesistente - deve lanciare eccezione")
//         void testProdottoInesistente() {
//             assertThrows(RuntimeException.class, () -> {
//                 prodottoService.getDettagliTracciabilita(999);
//             }, "Dovrebbe lanciare eccezione per prodotto inesistente");
//         }

//         @Test
//         @DisplayName("Prodotto senza processo di trasformazione - gestione corretta")
//         void testProdottoSenzaProcesso() {
//             // Crea un prodotto trasformato ma senza processo associato
//             Prodotto prodottoOrfano = new Prodotto(
//                     400, "Prodotto Orfano", "Prodotto senza processo",
//                     10.0, 5, trasformatoreLivello1);
//             prodottoOrfano.setTipoOrigine(TipoOrigineProdotto.TRASFORMATO);
//             prodottoRepository.save(prodottoOrfano);

//             TracciabilitaProdottoDTO tracciabilita = prodottoService.getDettagliTracciabilita(400);

//             assertNotNull(tracciabilita);
//             assertEquals("Prodotto Orfano", tracciabilita.getNomeProdotto());

//             // Senza processo di trasformazione, non può risalire al produttore originario
//             assertNull(tracciabilita.getProduttoreOriginario(),
//                     "Senza processo di trasformazione non dovrebbe identificare produttore originario");
//             assertTrue(tracciabilita.getProcessiTrasformazione().isEmpty(),
//                     "Non dovrebbe avere processi di trasformazione");
//         }

//         @Test
//         @DisplayName("Test protezione da cicli infiniti - simulazione catena circolare")
//         void testProtezioneCircolare() {
//             // Questo test verifica che la protezione da cicli infiniti funzioni
//             // Nel caso reale, i cicli sarebbero prevenuti dalla logica di business,
//             // ma la protezione deve esistere a livello di algoritmo

//             // Crea due prodotti che si referenziano a vicenda (scenario teorico)
//             Prodotto prodottoA = new Prodotto(
//                     501, "Prodotto A", "Test ciclo A", 5.0, 10, trasformatoreLivello1);
//             Prodotto prodottoB = new Prodotto(
//                     502, "Prodotto B", "Test ciclo B", 7.0, 10, trasformatoreLivello2);

//             prodottoA.setTipoOrigine(TipoOrigineProdotto.TRASFORMATO);
//             prodottoB.setTipoOrigine(TipoOrigineProdotto.TRASFORMATO);

//             // Crea processi che si referenziano a vicenda
//             ProcessoTrasformazione processoA = new ProcessoTrasformazione(
//                     "Processo A", "Test", trasformatoreLivello1,
//                     prodottoA, "Metodo Test");
//             processoA.setId(1991L);

//             ProcessoTrasformazione processoB = new ProcessoTrasformazione(
//                     "Processo B", "Test", trasformatoreLivello2,
//                     prodottoB, "Metodo Test");
//             processoB.setId(1992L);

//             // Crea fasi per entrambi i processi
//             FaseLavorazione faseA = new FaseLavorazione("Fase A", "Test fase A", 1, 10, true);
//             FaseLavorazione faseB = new FaseLavorazione("Fase B", "Test fase B", 1, 10, true);

//             // Crea input che si referenziano a vicenda
//             InputLavorazione inputB_per_A = new InputLavorazione(prodottoB, 1.0, "unità");
//             InputLavorazione inputA_per_B = new InputLavorazione(prodottoA, 1.0, "unità");

//             // Aggiungi gli input alle fasi
//             faseA.aggiungiInput(inputB_per_A);
//             faseB.aggiungiInput(inputA_per_B);

//             // Aggiungi fasi ai processi
//             processoA.aggiungiFase(faseA);
//             processoB.aggiungiFase(faseB);

//             prodottoA.setIdProcessoTrasformazioneOriginario(processoA.getId());
//             prodottoB.setIdProcessoTrasformazioneOriginario(processoB.getId());

//             // Aggiorniamo anche i prodotti nel repository dopo aver impostato l'ID del
//             // processo
//             prodottoRepository.save(prodottoA);
//             prodottoRepository.save(prodottoB);

//             prodottoRepository.save(prodottoA);
//             prodottoRepository.save(prodottoB);
//             processoRepository.save(processoA);
//             processoRepository.save(processoB);

//             // Il test dovrebbe completarsi senza entrare in un ciclo infinito
//             assertDoesNotThrow(() -> {
//                 TracciabilitaProdottoDTO tracciabilita = prodottoService.getDettagliTracciabilita(501);
//                 // Dovrebbe gestire il ciclo e restituire null per produttore originario
//                 assertNull(tracciabilita.getProduttoreOriginario(),
//                         "In presenza di cicli dovrebbe gestire gracefully e non trovare produttore originario");
//             }, "L'algoritmo dovrebbe gestire i cicli senza entrare in loop infinito");
//         }
//     }

//     @Nested
//     @DisplayName("Test Validazione Coerenza DTO")
//     class TestCoerenzaDTO {

//         @Test
//         @DisplayName("Verifica coerenza campi DTO con modelli sottostanti")
//         void testCoerenzaCampiDTO() {
//             TracciabilitaProdottoDTO tracciabilita = prodottoService.getDettagliTracciabilita(201);

//             // Verifica coerenza dati prodotto
//             Prodotto prodottoOriginale = prodottoRepository.findById(201);
//             assertNotNull(prodottoOriginale);

//             assertEquals(prodottoOriginale.getNome(), tracciabilita.getNomeProdotto());
//             assertEquals(prodottoOriginale.getDescrizione(), tracciabilita.getDescrizioneProdotto());
//             assertEquals(prodottoOriginale.getVenditore().getNome() + " " +
//                     prodottoOriginale.getVenditore().getCognome(), tracciabilita.getNomeVenditoreCorrente());
//             assertEquals(prodottoOriginale.getVenditore().getDatiAzienda().getNomeAzienda(),
//                     tracciabilita.getAziendaVenditoreCorrente());

//             // Verifica coerenza processo di trasformazione
//             TracciabilitaProcessoDTO processoDTO = tracciabilita.getProcessiTrasformazione().get(0);
//             // Ottiene il processo dal repository usando l'ID
//             Long processoId = prodottoOriginale.getIdProcessoTrasformazioneOriginario();
//             ProcessoTrasformazione processoOriginale = processoRepository.findById(processoId).orElse(null);

//             assertEquals(processoOriginale.getNome(), processoDTO.getNomeProcesso());
//             assertEquals(processoOriginale.getDescrizione(), processoDTO.getDescrizioneProcesso());
//             assertEquals(processoOriginale.getResponsabile().getNome(), processoDTO.getNomeResponsabile());
//             assertEquals(processoOriginale.getResponsabile().getCognome(), processoDTO.getCognomeResponsabile());

//             // Verifica che dataFineProcesso non sia hardcoded
//             if (processoOriginale.getDataFineProcesso() != null) {
//                 assertNotNull(processoDTO.getDataFineProcesso(),
//                         "dataFineProcesso deve essere mappato dal modello, non hardcoded");
//             }

//             // Verifica coerenza fasi
//             assertEquals(processoOriginale.getFasi().size(), processoDTO.getFasi().size());

//             for (int i = 0; i < processoOriginale.getFasi().size(); i++) {
//                 FaseLavorazione faseOriginale = processoOriginale.getFasi().get(i);
//                 TracciabilitaFaseDTO faseDTO = processoDTO.getFasi().get(i);

//                 assertEquals(faseOriginale.getNome(), faseDTO.getNomeFase());
//                 assertEquals(faseOriginale.getDescrizione(), faseDTO.getDescrizioneFase());
//                 assertEquals(faseOriginale.getDurataStimataMinuti(), faseDTO.getDurataMinuti());

//                 // Verifica che strumentiUtilizzati non sia hardcoded
//                 if (faseOriginale.getStrumentiUtilizzati() != null) {
//                     assertEquals(faseOriginale.getStrumentiUtilizzati(), faseDTO.getStrumentiUtilizzati(),
//                             "strumentiUtilizzati deve essere mappato dal modello, non essere 'N/A' hardcoded");
//                     assertNotEquals("N/A", faseDTO.getStrumentiUtilizzati(),
//                             "strumentiUtilizzati non dovrebbe essere il valore hardcoded 'N/A'");
//                 }
//             }

//             // Verifica coerenza input
//             assertEquals(processoOriginale.getInput().size(), processoDTO.getInput().size());
//         }

//         @Test
//         @DisplayName("Verifica che non ci siano valori hardcoded nei DTO")
//         void testAssenzaValoriHardcoded() {
//             TracciabilitaProdottoDTO tracciabilita = prodottoService.getDettagliTracciabilita(201);

//             TracciabilitaProcessoDTO processo = tracciabilita.getProcessiTrasformazione().get(0);

//             // Verifica che non ci siano campi con valori hardcoded tipici
//             for (TracciabilitaFaseDTO fase : processo.getFasi()) {
//                 assertNotEquals("N/A", fase.getStrumentiUtilizzati(),
//                         "strumentiUtilizzati non dovrebbe essere hardcoded come 'N/A'");
//                 assertNotEquals("", fase.getStrumentiUtilizzati(),
//                         "strumentiUtilizzati non dovrebbe essere vuoto");
//             }

//             // Verifica date non hardcoded
//             assertNotNull(processo.getDataInizioProcesso(), "dataInizioProcesso non dovrebbe essere null");
//             if (processo.getDataFineProcesso() != null) {
//                 assertNotEquals("1900-01-01", processo.getDataFineProcesso().toString().substring(0, 10),
//                         "dataFineProcesso non dovrebbe essere una data placeholder");
//             }
//         }
//     }
// }
