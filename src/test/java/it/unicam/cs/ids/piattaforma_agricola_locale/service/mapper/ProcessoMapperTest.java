package it.unicam.cs.ids.piattaforma_agricola_locale.service.mapper;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import it.unicam.cs.ids.piattaforma_agricola_locale.dto.processo.FaseLavorazioneDTO;
import it.unicam.cs.ids.piattaforma_agricola_locale.dto.processo.ProcessoTrasformazioneDTO;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.trasformazione.FaseLavorazione;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.trasformazione.FonteEsterna;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.trasformazione.FonteInterna;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.trasformazione.ProcessoTrasformazione;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.DatiAzienda;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.Produttore;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.TipoRuolo;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.Trasformatore;

class ProcessoMapperTest {

    private ProcessoMapper mapper;
    private Trasformatore trasformatore;
    private Produttore produttore;
    private ProcessoTrasformazione processo;

    @BeforeEach
    void setUp() {
        mapper = new ProcessoMapper();

        DatiAzienda datiAzienda = new DatiAzienda(1, "Azienda Test", "12345678901",
                "Via Test 1", "Descrizione azienda test", "logo.png", "www.test.com");
        trasformatore = new Trasformatore(1, "Mario", "Rossi", "<EMAIL>",
                "password", "1234567890", datiAzienda, TipoRuolo.TRASFORMATORE);

        DatiAzienda datiProduttore = new DatiAzienda(2, "Azienda Produttore", "98765432109",
                "Via Produttore 1", "Descrizione produttore", "logo2.png", "www.produttore.com");
        produttore = new Produttore(2, "Luigi", "Verdi", "<EMAIL>",
                "password", "0987654321", datiProduttore, TipoRuolo.PRODUTTORE);

        processo = new ProcessoTrasformazione("Processo Test",
                "Descrizione processo test", trasformatore);
        processo.setId(1L);
    }

    @Test
    void testToDto_ProcessoVuoto() {
        // Act
        ProcessoTrasformazioneDTO dto = mapper.toDto(processo);

        // Assert
        assertNotNull(dto);
        assertEquals(1L, dto.getIdProcesso());
        assertEquals("Processo Test", dto.getNomeProcesso());
        assertEquals("Descrizione processo test", dto.getDescrizioneProcesso());
        assertEquals("Mario", dto.getNomeTrasformatore());
        assertEquals("Rossi", dto.getCognomeTrasformatore());
        assertEquals("Azienda Test", dto.getAziendaTrasformatore());
        assertTrue(dto.getFasi().isEmpty());
    }

    @Test
    void testToDto_ProcessoConFasiFonteEsterna() {
        // Arrange
        FaseLavorazione fase1 = new FaseLavorazione("Fase 1", "Prima fase", 1,
                "Materia prima 1", new FonteEsterna("Fornitore Esterno"));
        FaseLavorazione fase2 = new FaseLavorazione("Fase 2", "Seconda fase", 2,
                "Materia prima 2", new FonteEsterna("Altro Fornitore"));

        processo.aggiungiFase(fase1);
        processo.aggiungiFase(fase2);

        // Act
        ProcessoTrasformazioneDTO dto = mapper.toDto(processo);

        // Assert
        assertNotNull(dto);
        assertEquals(2, dto.getFasi().size());

        FaseLavorazioneDTO faseDTO1 = dto.getFasi().get(0);
        assertEquals("Fase 1", faseDTO1.getNome());
        assertEquals("Prima fase", faseDTO1.getDescrizione());
        assertEquals(1, faseDTO1.getOrdineEsecuzione());
        assertEquals("Materia prima 1", faseDTO1.getMateriaPrimaUtilizzata());
        assertEquals("Fornitore Esterno", faseDTO1.getFonteMateriaPrima());

        FaseLavorazioneDTO faseDTO2 = dto.getFasi().get(1);
        assertEquals("Fase 2", faseDTO2.getNome());
        assertEquals("Seconda fase", faseDTO2.getDescrizione());
        assertEquals(2, faseDTO2.getOrdineEsecuzione());
        assertEquals("Materia prima 2", faseDTO2.getMateriaPrimaUtilizzata());
        assertEquals("Altro Fornitore", faseDTO2.getFonteMateriaPrima());
    }

    @Test
    void testToDto_ProcessoConFasiFonteInterna() {
        // Arrange
        FaseLavorazione fase1 = new FaseLavorazione("Fase 1", "Prima fase", 1,
                "Materia prima 1", new FonteInterna(produttore));

        processo.aggiungiFase(fase1);

        // Act
        ProcessoTrasformazioneDTO dto = mapper.toDto(processo);

        // Assert
        assertNotNull(dto);
        assertEquals(1, dto.getFasi().size());

        FaseLavorazioneDTO faseDTO = dto.getFasi().get(0);
        assertEquals("Fase 1", faseDTO.getNome());
        assertEquals("Prima fase", faseDTO.getDescrizione());
        assertEquals(1, faseDTO.getOrdineEsecuzione());
        assertEquals("Materia prima 1", faseDTO.getMateriaPrimaUtilizzata());
        assertEquals("Luigi Verdi - Azienda Produttore", faseDTO.getFonteMateriaPrima());
    }

    @Test
    void testToDto_ProcessoConFasiMiste() {
        // Arrange
        FaseLavorazione fase1 = new FaseLavorazione("Fase 1", "Prima fase", 1,
                "Materia prima 1", new FonteInterna(produttore));
        FaseLavorazione fase2 = new FaseLavorazione("Fase 2", "Seconda fase", 2,
                "Materia prima 2", new FonteEsterna("Fornitore Esterno"));

        processo.aggiungiFase(fase1);
        processo.aggiungiFase(fase2);

        // Act
        ProcessoTrasformazioneDTO dto = mapper.toDto(processo);

        // Assert
        assertNotNull(dto);
        assertEquals(2, dto.getFasi().size());

        // Verifica fase con fonte interna
        FaseLavorazioneDTO faseDTO1 = dto.getFasi().get(0);
        assertEquals("Luigi Verdi - Azienda Produttore", faseDTO1.getFonteMateriaPrima());

        // Verifica fase con fonte esterna
        FaseLavorazioneDTO faseDTO2 = dto.getFasi().get(1);
        assertEquals("Fornitore Esterno", faseDTO2.getFonteMateriaPrima());
    }

    @Test
    void testToDto_ProcessoNullo() {
        // Act
        ProcessoTrasformazioneDTO dto = mapper.toDto(null);

        // Assert
        assertNull(dto);
    }

    @Test
    void testToDto_ProcessoSenzaTrasformatore() {
        // Arrange & Act & Assert
        assertThrows(NullPointerException.class, () -> {
            new ProcessoTrasformazione("Processo Test", "Descrizione", null);
        });
    }

    @Test
    void testToDto_TrasformatoreConDatiAziendaNulli() {
        // Arrange
        Trasformatore trasformatoreNoDati = new Trasformatore(3, "Giuseppe", "Bianchi",
                "<EMAIL>", "password", "3333333333", null, TipoRuolo.TRASFORMATORE);
        ProcessoTrasformazione processoNoDati = new ProcessoTrasformazione(
                "Processo Test", "Descrizione", trasformatoreNoDati);
        processoNoDati.setId(2L);

        // Act
        ProcessoTrasformazioneDTO dto = mapper.toDto(processoNoDati);

        // Assert
        assertNotNull(dto);
        assertEquals("Giuseppe", dto.getNomeTrasformatore());
        assertEquals("Bianchi", dto.getCognomeTrasformatore());
        assertEquals("N/D", dto.getAziendaTrasformatore());
    }

    @Test
    void testOrdinamentoFasiPerOrdineEsecuzione() {
        // Arrange - Aggiungi fasi in ordine non sequenziale
        FaseLavorazione fase3 = new FaseLavorazione("Fase 3", "Terza fase", 3,
                "Materia prima 3", new FonteEsterna("Fornitore 3"));
        FaseLavorazione fase1 = new FaseLavorazione("Fase 1", "Prima fase", 1,
                "Materia prima 1", new FonteEsterna("Fornitore 1"));
        FaseLavorazione fase2 = new FaseLavorazione("Fase 2", "Seconda fase", 2,
                "Materia prima 2", new FonteEsterna("Fornitore 2"));

        processo.aggiungiFase(fase3);
        processo.aggiungiFase(fase1);
        processo.aggiungiFase(fase2);

        // Act
        ProcessoTrasformazioneDTO dto = mapper.toDto(processo);

        // Assert - Le fasi dovrebbero essere ordinate per ordineEsecuzione
        assertEquals(3, dto.getFasi().size());
        assertEquals(1, dto.getFasi().get(0).getOrdineEsecuzione());
        assertEquals(2, dto.getFasi().get(1).getOrdineEsecuzione());
        assertEquals(3, dto.getFasi().get(2).getOrdineEsecuzione());
    }
}