package it.unicam.cs.ids.piattaforma_agricola_locale.model.trasformazione;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.DatiAzienda;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.Produttore;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.TipoRuolo;

class FonteMateriaPrimaTest {

    private Produttore produttore;

    @BeforeEach
    void setUp() {
        DatiAzienda datiAzienda = new DatiAzienda(1, "Azienda Agricola Rossi", "12345678901", 
            "Via Campagna 1", "Produzione biologica", "logo.png", "www.rossi.it");
        produttore = new Produttore(1, "<PERSON>", "<PERSON>", "<EMAIL>", 
            "password", "1234567890", datiAzienda, TipoRuolo.PRODUTTORE);
    }

    @Test
    void testFonteEsternaCreazione() {
        // Arrange & Act
        FonteEsterna fonte = new FonteEsterna("Fornitore Esterno Srl");

        // Assert
        assertNotNull(fonte);
        assertEquals("Fornitore Esterno Srl", fonte.getDescrizione());
        assertEquals("Fornitore Esterno Srl", fonte.getNomeFornitore());
    }

    @Test
    void testFonteEsternaDescrizioneNulla() {
        // Act & Assert
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> new FonteEsterna(null)
        );
        assertEquals("Il nome del fornitore non può essere nullo o vuoto", exception.getMessage());
    }

    @Test
    void testFonteEsternaDescrizioneVuota() {
        // Act & Assert
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> new FonteEsterna("   ")
        );
        assertEquals("Il nome del fornitore non può essere nullo o vuoto", exception.getMessage());
    }

    @Test
    void testFonteInternaCreazione() {
        // Arrange & Act
        FonteInterna fonte = new FonteInterna(produttore);

        // Assert
        assertNotNull(fonte);
        assertEquals("Mario Rossi - Azienda Agricola Rossi", fonte.getDescrizione());
        assertEquals(produttore, fonte.getProduttore());
    }

    @Test
    void testFonteInternaProduttoreNullo() {
        // Act & Assert
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> new FonteInterna(null)
        );
        assertEquals("Il produttore non può essere nullo", exception.getMessage());
    }

    @Test
    void testFonteInternaProduttoreSenzaDatiAzienda() {
        // Arrange
        Produttore produttoreSenzaDati = new Produttore(2, "Luigi", "Verdi", "<EMAIL>", 
            "password", "0987654321", null, TipoRuolo.PRODUTTORE);

        // Act
        FonteInterna fonte = new FonteInterna(produttoreSenzaDati);

        // Assert
        assertEquals("Luigi Verdi - N/D", fonte.getDescrizione());
    }

    @Test
    void testFonteInternaConDatiAziendaSenzaNome() {
        // Arrange
        DatiAzienda datiSenzaNome = new DatiAzienda(3, null, "98765432109", 
            "Via Test 1", "Descrizione", "logo.png", "www.test.com");
        Produttore produttoreConDatiIncompleti = new Produttore(3, "Giuseppe", "Bianchi", 
            "<EMAIL>", "password", "3333333333", datiSenzaNome, TipoRuolo.PRODUTTORE);

        // Act
        FonteInterna fonte = new FonteInterna(produttoreConDatiIncompleti);

        // Assert
        assertEquals("Giuseppe Bianchi - N/D", fonte.getDescrizione());
    }

    @Test
    void testPolimorfismoFonteMateriaPrima() {
        // Arrange
        FonteMateriaPrima fonteEsterna = new FonteEsterna("Fornitore ABC");
        FonteMateriaPrima fonteInterna = new FonteInterna(produttore);

        // Act & Assert
        assertEquals("Fornitore ABC", fonteEsterna.getDescrizione());
        assertEquals("Mario Rossi - Azienda Agricola Rossi", fonteInterna.getDescrizione());
        
        // Verifica che entrambe implementino l'interfaccia
        assertTrue(fonteEsterna instanceof FonteMateriaPrima);
        assertTrue(fonteInterna instanceof FonteMateriaPrima);
    }

    @Test
    void testEqualsEHashCodeFonteEsterna() {
        // Arrange
        FonteEsterna fonte1 = new FonteEsterna("Fornitore Test");
        FonteEsterna fonte2 = new FonteEsterna("Fornitore Test");
        FonteEsterna fonte3 = new FonteEsterna("Altro Fornitore");

        // Act & Assert
        assertEquals(fonte1, fonte2);
        assertNotEquals(fonte1, fonte3);
        assertEquals(fonte1.hashCode(), fonte2.hashCode());
        assertNotEquals(fonte1.hashCode(), fonte3.hashCode());
    }

    @Test
    void testEqualsEHashCodeFonteInterna() {
        // Arrange
        DatiAzienda altriDati = new DatiAzienda(2, "Altra Azienda", "98765432109", 
            "Via Altra 1", "Altra descrizione", "altro_logo.png", "www.altra.it");
        Produttore altroProduttore = new Produttore(2, "Luigi", "Verdi", "<EMAIL>", 
            "password", "0987654321", altriDati, TipoRuolo.PRODUTTORE);

        FonteInterna fonte1 = new FonteInterna(produttore);
        FonteInterna fonte2 = new FonteInterna(produttore);
        FonteInterna fonte3 = new FonteInterna(altroProduttore);

        // Act & Assert
        assertEquals(fonte1, fonte2);
        assertNotEquals(fonte1, fonte3);
        assertEquals(fonte1.hashCode(), fonte2.hashCode());
        assertNotEquals(fonte1.hashCode(), fonte3.hashCode());
    }

    @Test
    void testToStringFonteEsterna() {
        // Arrange
        FonteEsterna fonte = new FonteEsterna("Fornitore XYZ");

        // Act
        String toString = fonte.toString();

        // Assert
        assertTrue(toString.contains("FonteEsterna"));
        assertTrue(toString.contains("Fornitore XYZ"));
    }

    @Test
    void testToStringFonteInterna() {
        // Arrange
        FonteInterna fonte = new FonteInterna(produttore);

        // Act
        String toString = fonte.toString();

        // Assert
        assertTrue(toString.contains("FonteInterna"));
        assertTrue(toString.contains("Mario Rossi"));
        assertTrue(toString.contains("Azienda Agricola Rossi"));
    }

    @Test
    void testComparazioneTipiDiversi() {
        // Arrange
        FonteEsterna fonteEsterna = new FonteEsterna("Fornitore");
        FonteInterna fonteInterna = new FonteInterna(produttore);

        // Act & Assert
        assertNotEquals(fonteEsterna, fonteInterna);
        assertNotEquals(fonteInterna, fonteEsterna);
        assertNotEquals(fonteEsterna.hashCode(), fonteInterna.hashCode());
    }

    @Test
    void testDescrizioniComplesse() {
        // Test con caratteri speciali e lunghe
        FonteEsterna fonteComplessa = new FonteEsterna(
            "Società Agricola Bio-Naturale S.r.l. - Divisione Forniture Specializzate (Lotto #2024-BIO-001)"
        );

        assertEquals(
            "Società Agricola Bio-Naturale S.r.l. - Divisione Forniture Specializzate (Lotto #2024-BIO-001)",
            fonteComplessa.getDescrizione()
        );
    }

    @Test
    void testCasiLimiteNomiProduttore() {
        // Test con nomi molto lunghi
        DatiAzienda datiLunghi = new DatiAzienda(4, 
            "Azienda Agricola Biologica Certificata della Valle del Sole e della Luna Società Cooperativa a Responsabilità Limitata",
            "11111111111", "Via Lunghissima 123", "Descrizione", "logo.png", "www.lungo.it");
        
        Produttore produttoreLungo = new Produttore(4, 
            "Giovanni Battista Alessandro", 
            "Della Valle Del Sole E Della Luna", 
            "<EMAIL>", "password", "4444444444", datiLunghi, TipoRuolo.PRODUTTORE);

        FonteInterna fonteLunga = new FonteInterna(produttoreLungo);

        String descrizione = fonteLunga.getDescrizione();
        assertTrue(descrizione.contains("Giovanni Battista Alessandro"));
        assertTrue(descrizione.contains("Della Valle Del Sole E Della Luna"));
        assertTrue(descrizione.contains("Azienda Agricola Biologica Certificata"));
    }
}