package it.unicam.cs.ids.piattaforma_agricola_locale.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import it.unicam.cs.ids.piattaforma_agricola_locale.model.repository.IProcessoTrasformazioneRepository;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.trasformazione.FaseLavorazione;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.trasformazione.FonteEsterna;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.trasformazione.FonteInterna;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.trasformazione.ProcessoTrasformazione;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.DatiAzienda;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.Produttore;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.TipoRuolo;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.Trasformatore;

@ExtendWith(MockitoExtension.class)
class ProcessoTrasformazioneServiceTest {

    @Mock
    private IProcessoTrasformazioneRepository processoRepository;

    @InjectMocks
    private ProcessoTrasformazioneService processoService;

    private Trasformatore trasformatore;
    private Produttore produttore;
    private ProcessoTrasformazione processo;

    @BeforeEach
    void setUp() {
        DatiAzienda datiAzienda = new DatiAzienda(1, "Azienda Test", "12345678901",
                "Via Test 1", "Descrizione azienda test", "logo.png", "www.test.com");
        trasformatore = new Trasformatore(1, "Mario", "Rossi", "<EMAIL>",
                "password", "1234567890", datiAzienda, TipoRuolo.TRASFORMATORE);

        DatiAzienda datiProduttore = new DatiAzienda(2, "Azienda Produttore", "98765432109",
                "Via Produttore 1", "Descrizione produttore", "logo2.png", "www.produttore.com");
        produttore = new Produttore(2, "Luigi", "Verdi", "<EMAIL>",
                "password", "0987654321", datiProduttore, TipoRuolo.PRODUTTORE);

        processo = new ProcessoTrasformazione("Processo Test",
                "Descrizione processo test", trasformatore);
        processo.setId(1L);
    }

    @Test
    void testCreaProcessoValido() {
        // Arrange
        when(processoRepository.existsByNomeAndTrasformatore("Processo Test", trasformatore))
                .thenReturn(false);
        when(processoRepository.save(any(ProcessoTrasformazione.class)))
                .thenReturn(processo);

        // Act
        ProcessoTrasformazione risultato = processoService.creaProcesso(
                "Processo Test", "Descrizione processo test", trasformatore, "Metodo Test");

        // Assert
        assertNotNull(risultato);
        assertEquals("Processo Test", risultato.getNome());
        assertEquals("Descrizione processo test", risultato.getDescrizione());
        assertEquals(trasformatore, risultato.getTrasformatore());
        verify(processoRepository).save(any(ProcessoTrasformazione.class));
    }

    @Test
    void testCreaProcessoNomeNullo() {
        // Act & Assert
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> processoService.creaProcesso(null, "Descrizione", trasformatore, null));
        assertEquals("Il nome del processo non può essere nullo o vuoto", exception.getMessage());
    }

    @Test
    void testCreaProcessoNomeVuoto() {
        // Act & Assert
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> processoService.creaProcesso("   ", "Descrizione", trasformatore, null));
        assertEquals("Il nome del processo non può essere nullo o vuoto", exception.getMessage());
    }

    @Test
    void testCreaProcessoDescrizioneNulla() {
        // Act & Assert
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> processoService.creaProcesso("Nome", null, trasformatore, null));
        assertEquals("La descrizione del processo non può essere nulla o vuota", exception.getMessage());
    }

    @Test
    void testCreaProcessoTrasformatoreNullo() {
        // Act & Assert
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> processoService.creaProcesso("Nome", "Descrizione", null, null));
        assertEquals("Il trasformatore non può essere nullo", exception.getMessage());
    }

    @Test
    void testCreaProcessoNomeDuplicato() {
        // Arrange
        when(processoRepository.existsByNomeAndTrasformatore("Processo Test", trasformatore))
                .thenReturn(true);

        // Act & Assert
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> processoService.creaProcesso("Processo Test", "Descrizione", trasformatore, null));
        assertTrue(exception.getMessage().contains("Esiste già un processo con il nome"));
    }

    @Test
    void testAggiungiFaseAlProcessoConFonteEsterna() {
        // Arrange
        FaseLavorazione fase = new FaseLavorazione("Fase Test", "Descrizione fase", 1,
                "Materia prima test", new FonteEsterna("Fornitore Test"));

        when(processoRepository.findById(1L)).thenReturn(Optional.of(processo));
        when(processoRepository.save(any(ProcessoTrasformazione.class))).thenReturn(processo);

        // Act
        ProcessoTrasformazione risultato = processoService.aggiungiFaseAlProcesso(1L, fase);

        // Assert
        assertNotNull(risultato);
        verify(processoRepository).save(processo);
    }

    @Test
    void testAggiungiFaseAlProcessoConFonteInterna() {
        // Arrange
        FaseLavorazione fase = new FaseLavorazione("Fase Test", "Descrizione fase", 1,
                "Materia prima test", new FonteInterna(produttore));

        when(processoRepository.findById(1L)).thenReturn(Optional.of(processo));
        when(processoRepository.save(any(ProcessoTrasformazione.class))).thenReturn(processo);

        // Act
        ProcessoTrasformazione risultato = processoService.aggiungiFaseAlProcesso(1L, fase);

        // Assert
        assertNotNull(risultato);
        verify(processoRepository).save(processo);
    }

    @Test
    void testAggiungiFaseProcessoInesistente() {
        // Arrange
        FaseLavorazione fase = new FaseLavorazione("Fase Test", "Descrizione fase", 1,
                "Materia prima test", new FonteEsterna("Fornitore Test"));

        when(processoRepository.findById(999L)).thenReturn(Optional.empty());

        // Act & Assert
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> processoService.aggiungiFaseAlProcesso(999L, fase));
        assertTrue(exception.getMessage().contains("Processo con ID 999 non trovato"));
    }

    @Test
    void testAggiungiFaseNulla() {
        // Arrange
        when(processoRepository.findById(1L)).thenReturn(Optional.of(processo));

        // Act & Assert
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> processoService.aggiungiFaseAlProcesso(1L, null));
        assertEquals("La fase non può essere nulla", exception.getMessage());
    }

    @Test
    void testEliminaProcessoAutorizzato() {
        // Arrange
        when(processoRepository.findById(1L)).thenReturn(Optional.of(processo));
        when(processoRepository.deleteById(1L)).thenReturn(true);

        // Act
        boolean risultato = processoService.eliminaProcesso(1L, trasformatore);

        // Assert
        assertTrue(risultato);
        verify(processoRepository).deleteById(1L);
    }

    @Test
    void testEliminaProcessoNonAutorizzato() {
        // Arrange
        DatiAzienda altriDati = new DatiAzienda(3, "Altra Azienda", "98765432109",
                "Via Altra 1", "Altra descrizione", "altro_logo.png", "www.altro.com");
        Trasformatore altroTrasformatore = new Trasformatore(3, "Luigi", "Verdi",
                "<EMAIL>", "password", "0987654321", altriDati, TipoRuolo.TRASFORMATORE);

        when(processoRepository.findById(1L)).thenReturn(Optional.of(processo));

        // Act & Assert
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> processoService.eliminaProcesso(1L, altroTrasformatore));
        assertTrue(exception.getMessage().contains("Non autorizzato"));
    }

    @Test
    void testEliminaProcessoInesistente() {
        // Arrange
        when(processoRepository.findById(999L)).thenReturn(Optional.empty());

        // Act & Assert
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> processoService.eliminaProcesso(999L, trasformatore));
        assertTrue(exception.getMessage().contains("Processo con ID 999 non trovato"));
    }

    @Test
    void testValidaSequenzaFasiCorretta() {
        // Arrange
        FaseLavorazione fase1 = new FaseLavorazione("Fase 1", "Prima fase", 1,
                "Materia prima 1", new FonteEsterna("Fornitore 1"));
        FaseLavorazione fase2 = new FaseLavorazione("Fase 2", "Seconda fase", 2,
                "Materia prima 2", new FonteEsterna("Fornitore 2"));

        processo.aggiungiFase(fase1);
        processo.aggiungiFase(fase2);

        // Act & Assert - Verifica che le fasi siano state aggiunte correttamente
        assertEquals(2, processo.getFasi().size());
        assertEquals(1, processo.getFasi().get(0).getOrdineEsecuzione());
        assertEquals(2, processo.getFasi().get(1).getOrdineEsecuzione());
    }

    @Test
    void testValidaSequenzaFasiDuplicata() {
        // Arrange
        FaseLavorazione fase1 = new FaseLavorazione("Fase 1", "Prima fase", 1,
                "Materia prima 1", new FonteEsterna("Fornitore 1"));
        FaseLavorazione fase2 = new FaseLavorazione("Fase 2", "Seconda fase", 1,
                "Materia prima 2", new FonteEsterna("Fornitore 2"));

        // Act & Assert - Il processo dovrebbe gestire sequenze duplicate
        processo.aggiungiFase(fase1);
        processo.aggiungiFase(fase2);

        // Verifica che entrambe le fasi siano state aggiunte
        assertEquals(2, processo.getFasi().size());
    }

}