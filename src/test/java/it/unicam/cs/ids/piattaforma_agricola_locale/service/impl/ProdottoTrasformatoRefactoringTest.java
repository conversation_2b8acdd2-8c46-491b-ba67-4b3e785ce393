package it.unicam.cs.ids.piattaforma_agricola_locale.service.impl;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import it.unicam.cs.ids.piattaforma_agricola_locale.model.catalogo.Prodotto;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.catalogo.TipoOrigineProdotto;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.repository.ProdottoRepository;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.repository.UtenteRepository;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.DatiAzienda;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.Produttore;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.TipoRuolo;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.Trasformatore;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.Venditore;

/**
 * Test per verificare il corretto funzionamento del refactoring dei metodi
 * per la gestione dei prodotti trasformati spostati da Trasformatore a
 * ProdottoService.
 */
class ProdottoTrasformatoRefactoringTest {

    private ProdottoService prodottoService;
    private ProdottoRepository prodottoRepository;
    private UtenteRepository utenteRepository;
    private Trasformatore trasformatore;
    private Venditore venditoreRegolare;

    @BeforeEach
    void setUp() {
        prodottoRepository = new ProdottoRepository();
        utenteRepository = new UtenteRepository();
        prodottoService = new ProdottoService(prodottoRepository, null, utenteRepository);

        // Crea un trasformatore per i test
        DatiAzienda datiAzienda = new DatiAzienda(
                1,
                "Caseificio Test",
                "12345678901",
                "Via Test 123",
                "Test trasformazione",
                "",
                "www.test.it");

        trasformatore = new Trasformatore(
                1,
                "Marco",
                "Rossi",
                "<EMAIL>",
                "password123",
                "1234567890",
                datiAzienda,
                TipoRuolo.TRASFORMATORE);

        // Crea un venditore regolare per testare le restrizioni
        DatiAzienda datiAzienda2 = new DatiAzienda(
                2,
                "Azienda Regolare",
                "98765432101",
                "Via Regolare 456",
                "Venditore regolare",
                "",
                "www.regolare.it");

        venditoreRegolare = new Produttore(
                2,
                "Luca",
                "Bianchi",
                "<EMAIL>",
                "password123",
                "0987654321",
                datiAzienda2,
                TipoRuolo.PRODUTTORE);
    }

    @Test
    @DisplayName("Test creazione prodotto trasformato con successo")
    void testAggiungiProdottoTrasformatoSuccesso() {
        // Arrange
        String nome = "Formaggio Pecorino";
        String descrizione = "Formaggio pecorino stagionato 6 mesi";
        double prezzo = 25.90;
        int quantita = 10;
        Long idProcesso = 123L;

        // Act
        Prodotto prodotto = prodottoService.aggiungiProdottoTrasformato(
                nome, descrizione, prezzo, quantita, trasformatore, idProcesso);

        // Assert
        assertNotNull(prodotto);
        assertEquals(nome, prodotto.getNome());
        assertEquals(descrizione, prodotto.getDescrizione());
        assertEquals(prezzo, prodotto.getPrezzo());
        assertEquals(quantita, prodotto.getQuantitaDisponibile());
        assertEquals(trasformatore, prodotto.getVenditore());
        assertEquals(TipoOrigineProdotto.TRASFORMATO, prodotto.getTipoOrigine());
        assertEquals(idProcesso, prodotto.getIdProcessoTrasformazioneOriginario());
        assertTrue(trasformatore.getProdottiOfferti().contains(prodotto));
    }

    @Test
    @DisplayName("Test creazione prodotto trasformato con venditore non trasformatore fallisce")
    void testAggiungiProdottoTrasformatoVenditoreNonTrasformatore() {
        // Arrange
        String nome = "Prodotto Test";
        String descrizione = "Test";
        double prezzo = 10.0;
        int quantita = 5;
        Long idProcesso = 123L;

        // Act & Assert
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> prodottoService.aggiungiProdottoTrasformato(
                        nome, descrizione, prezzo, quantita, venditoreRegolare, idProcesso));

        assertEquals("Solo i trasformatori possono creare prodotti trasformati", exception.getMessage());
    }

    @Test
    @DisplayName("Test creazione prodotto trasformato con ID processo nullo fallisce")
    void testAggiungiProdottoTrasformatoIdProcessoNullo() {
        // Arrange
        String nome = "Prodotto Test";
        String descrizione = "Test";
        double prezzo = 10.0;
        int quantita = 5;
        Long idProcesso = null;

        // Act & Assert
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> prodottoService.aggiungiProdottoTrasformato(
                        nome, descrizione, prezzo, quantita, trasformatore, idProcesso));

        assertEquals("L'ID del processo di trasformazione non può essere nullo", exception.getMessage());
    }

    @Test
    @DisplayName("Test impostazione prodotto esistente come trasformato")
    void testImpostaProdottoComeTrasformato() {
        // Arrange
        Prodotto prodotto = prodottoService.creaProdotto(
                "Latte Fresco", "Latte di mucca", 2.50, 20, trasformatore);
        Long idProcesso = 456L;

        // Act
        prodottoService.impostaProdottoComeTrasformato(prodotto, trasformatore, idProcesso);

        // Assert
        assertEquals(TipoOrigineProdotto.TRASFORMATO, prodotto.getTipoOrigine());
        assertEquals(idProcesso, prodotto.getIdProcessoTrasformazioneOriginario());
        assertTrue(trasformatore.getProdottiOfferti().contains(prodotto));
    }

    @Test
    @DisplayName("Test impostazione prodotto come trasformato con parametri nulli fallisce")
    void testImpostaProdottoComeTrasformatoParametriNulli() {
        // Arrange
        Prodotto prodotto = prodottoService.creaProdotto(
                "Test", "Test", 1.0, 1, trasformatore);
        Long idProcesso = 456L;

        // Act & Assert - Prodotto nullo
        assertThrows(IllegalArgumentException.class,
                () -> prodottoService.impostaProdottoComeTrasformato(null, trasformatore, idProcesso));

        // Act & Assert - Venditore nullo
        assertThrows(IllegalArgumentException.class,
                () -> prodottoService.impostaProdottoComeTrasformato(prodotto, null, idProcesso));

        // Act & Assert - ID processo nullo
        assertThrows(IllegalArgumentException.class,
                () -> prodottoService.impostaProdottoComeTrasformato(prodotto, trasformatore, null));
    }

    @Test
    @DisplayName("Test impostazione prodotto come trasformato con venditore non proprietario fallisce")
    void testImpostaProdottoComeTrasformatoVenditoreNonProprietario() {
        // Arrange
        Prodotto prodotto = prodottoService.creaProdotto(
                "Test", "Test", 1.0, 1, trasformatore);
        Long idProcesso = 456L;

        // Act & Assert
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> prodottoService.impostaProdottoComeTrasformato(prodotto, venditoreRegolare, idProcesso));

        assertEquals("Il prodotto non appartiene al venditore specificato", exception.getMessage());
    }

    @Test
    @DisplayName("Test persistenza dei dati nel repository")
    void testPersistenzaDati() {
        // Arrange
        String nome = "Olio Extravergine";
        String descrizione = "Olio EVO prodotto da olive locali";
        double prezzo = 15.0;
        int quantita = 50;
        Long idProcesso = 789L;

        // Act
        Prodotto prodotto = prodottoService.aggiungiProdottoTrasformato(
                nome, descrizione, prezzo, quantita, trasformatore, idProcesso);

        // Assert - Verifica che il prodotto sia nel repository
        Prodotto prodottoSalvato = prodottoRepository.findById(prodotto.getId());
        assertNotNull(prodottoSalvato);
        assertEquals(TipoOrigineProdotto.TRASFORMATO, prodottoSalvato.getTipoOrigine());
        assertEquals(idProcesso, prodottoSalvato.getIdProcessoTrasformazioneOriginario());

        // Assert - Verifica che il trasformatore sia aggiornato nel repository utenti
        // (Questo dipende dall'implementazione del repository)
        assertTrue(trasformatore.getProdottiOfferti().contains(prodotto));
    }
}
