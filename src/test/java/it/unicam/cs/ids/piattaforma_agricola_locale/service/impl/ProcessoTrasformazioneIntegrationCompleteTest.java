package it.unicam.cs.ids.piattaforma_agricola_locale.service.impl;

import static org.junit.jupiter.api.Assertions.*;

import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import it.unicam.cs.ids.piattaforma_agricola_locale.dto.processo.FaseLavorazioneDTO;
import it.unicam.cs.ids.piattaforma_agricola_locale.dto.processo.ProcessoTrasformazioneDTO;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.repository.ProcessoTrasformazioneRepository;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.trasformazione.FaseLavorazione;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.trasformazione.FonteEsterna;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.trasformazione.FonteInterna;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.trasformazione.ProcessoTrasformazione;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.DatiAzienda;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.Produttore;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.TipoRuolo;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.Trasformatore;
import it.unicam.cs.ids.piattaforma_agricola_locale.service.mapper.ProcessoMapper;

/**
 * Test di integrazione completo per verificare il funzionamento
 * del sistema di processi di trasformazione end-to-end.
 */
class ProcessoTrasformazioneIntegrationCompleteTest {

    private ProcessoTrasformazioneService processoService;
    private ProcessoTrasformazioneRepository repository;
    private ProcessoMapper mapper;
    private Trasformatore trasformatore;
    private Produttore produttore;

    @BeforeEach
    void setUp() {
        // Setup repository in-memory
        repository = new ProcessoTrasformazioneRepository();
        processoService = new ProcessoTrasformazioneService(repository);
        mapper = new ProcessoMapper();

        // Setup utenti
        DatiAzienda datiTrasformatore = new DatiAzienda(1, "Caseificio Alpino", "12345678901",
                "Via Montagna 1", "Produzione formaggi di montagna", "logo_caseificio.png", "www.caseificioalpino.it");
        trasformatore = new Trasformatore(1, "Marco", "Bianchi", "<EMAIL>",
                "password", "1234567890", datiTrasformatore, TipoRuolo.TRASFORMATORE);

        DatiAzienda datiProduttore = new DatiAzienda(2, "Allevamento Valle Verde", "98765432109",
                "Via Pascoli 5", "Allevamento bovini da latte", "logo_allevamento.png", "www.valleverde.it");
        produttore = new Produttore(2, "Giuseppe", "Rossi", "<EMAIL>",
                "password", "0987654321", datiProduttore, TipoRuolo.PRODUTTORE);
    }

    @Test
    void testWorkflowCompletoProduzioneFormaggio() {
        // 1. Creazione processo di trasformazione
        ProcessoTrasformazione processo = processoService.creaProcesso(
                "Produzione Gorgonzola DOP",
                "Processo tradizionale per la produzione di Gorgonzola DOP",
                trasformatore,
                "Metodo tradizionale con stagionatura in grotte naturali");

        assertNotNull(processo);
        assertEquals("Produzione Gorgonzola DOP", processo.getNome());
        assertEquals(trasformatore, processo.getTrasformatore());

        // 2. Aggiunta fasi di lavorazione

        // Fase 1: Raccolta latte (fonte interna)
        FaseLavorazione fase1 = new FaseLavorazione(
                "Raccolta Latte",
                "Raccolta del latte fresco dalle mucche dell'allevamento partner",
                1,
                "Latte fresco intero",
                new FonteInterna(produttore));

        processo = processoService.aggiungiFaseAlProcesso(processo.getId(), fase1);

        // Fase 2: Pastorizzazione (fonte esterna per enzimi)
        FaseLavorazione fase2 = new FaseLavorazione(
                "Pastorizzazione",
                "Riscaldamento del latte a 72°C per 15 secondi",
                2,
                "Enzimi lattici",
                new FonteEsterna("Fornitore Enzimi Bio Srl"));

        processo = processoService.aggiungiFaseAlProcesso(processo.getId(), fase2);

        // Fase 3: Cagliatura
        FaseLavorazione fase3 = new FaseLavorazione(
                "Cagliatura",
                "Aggiunta di caglio e formazione della cagliata",
                3,
                "Caglio naturale",
                new FonteEsterna("Caseificio Tradizionale Lombardo"));

        processo = processoService.aggiungiFaseAlProcesso(processo.getId(), fase3);

        // Fase 4: Stagionatura
        FaseLavorazione fase4 = new FaseLavorazione(
                "Stagionatura",
                "Stagionatura in grotte naturali per 60 giorni",
                4,
                "Muffe Penicillium",
                new FonteEsterna("Laboratorio Microbiologico Alpino"));

        processo = processoService.aggiungiFaseAlProcesso(processo.getId(), fase4);

        // 3. Verifica struttura processo
        assertEquals(4, processo.getFasi().size());

        // Verifica ordine fasi
        List<FaseLavorazione> fasi = processo.getFasi();
        assertEquals(1, fasi.get(0).getOrdineEsecuzione());
        assertEquals(2, fasi.get(1).getOrdineEsecuzione());
        assertEquals(3, fasi.get(2).getOrdineEsecuzione());
        assertEquals(4, fasi.get(3).getOrdineEsecuzione());

        // 4. Conversione a DTO per API
        ProcessoTrasformazioneDTO dto = mapper.toDto(processo);

        assertNotNull(dto);
        assertEquals("Produzione Gorgonzola DOP", dto.getNomeProcesso());
        assertEquals("Marco", dto.getNomeTrasformatore());
        assertEquals("Bianchi", dto.getCognomeTrasformatore());
        assertEquals("Caseificio Alpino", dto.getAziendaTrasformatore());
        assertEquals(4, dto.getFasi().size());

        // 5. Verifica dettagli fasi nel DTO
        List<FaseLavorazioneDTO> fasiDTO = dto.getFasi();

        // Fase 1 - Fonte interna
        FaseLavorazioneDTO fase1DTO = fasiDTO.get(0);
        assertEquals("Raccolta Latte", fase1DTO.getNome());
        assertEquals("Latte fresco intero", fase1DTO.getMateriaPrimaUtilizzata());
        assertEquals("Giuseppe Rossi - Allevamento Valle Verde", fase1DTO.getFonteMateriaPrima());

        // Fase 2 - Fonte esterna
        FaseLavorazioneDTO fase2DTO = fasiDTO.get(1);
        assertEquals("Pastorizzazione", fase2DTO.getNome());
        assertEquals("Fornitore Enzimi Bio Srl", fase2DTO.getFonteMateriaPrima());

        // Fase 4 - Ultima fase
        FaseLavorazioneDTO fase4DTO = fasiDTO.get(3);
        assertEquals("Stagionatura", fase4DTO.getNome());
        assertEquals("Laboratorio Microbiologico Alpino", fase4DTO.getFonteMateriaPrima());

        // 6. Test ricerca processi per trasformatore
        List<ProcessoTrasformazione> processiTrasformatore = repository.findByTrasformatore(trasformatore);
        assertEquals(1, processiTrasformatore.size());
        assertEquals(processo.getId(), processiTrasformatore.get(0).getId());
    }

    @Test
    void testWorkflowMultipliProcessi() {
        // Crea primo processo
        ProcessoTrasformazione processo1 = processoService.creaProcesso(
                "Produzione Mozzarella",
                "Processo per mozzarella fresca",
                trasformatore,
                "Metodo tradizionale campano");

        // Crea secondo processo
        ProcessoTrasformazione processo2 = processoService.creaProcesso(
                "Produzione Ricotta",
                "Processo per ricotta fresca",
                trasformatore,
                "Utilizzo siero residuo");

        // Aggiungi fasi al primo processo
        FaseLavorazione fase1P1 = new FaseLavorazione(
                "Riscaldamento Latte", "Riscaldamento a 35°C", 1,
                "Latte fresco", new FonteInterna(produttore));
        processo1 = processoService.aggiungiFaseAlProcesso(processo1.getId(), fase1P1);

        // Aggiungi fasi al secondo processo
        FaseLavorazione fase1P2 = new FaseLavorazione(
                "Raccolta Siero", "Raccolta siero dalla produzione mozzarella", 1,
                "Siero di latte", new FonteEsterna("Recupero interno"));
        processo2 = processoService.aggiungiFaseAlProcesso(processo2.getId(), fase1P2);

        // Verifica che entrambi i processi esistano
        List<ProcessoTrasformazione> processi = repository.findByTrasformatore(trasformatore);
        assertEquals(2, processi.size());

        // Verifica che ogni processo abbia le sue fasi
        ProcessoTrasformazione processoRecuperato1 = repository.findById(processo1.getId()).orElse(null);
        ProcessoTrasformazione processoRecuperato2 = repository.findById(processo2.getId()).orElse(null);

        assertNotNull(processoRecuperato1);
        assertNotNull(processoRecuperato2);
        assertEquals(1, processoRecuperato1.getFasi().size());
        assertEquals(1, processoRecuperato2.getFasi().size());
        assertEquals("Riscaldamento Latte", processoRecuperato1.getFasi().get(0).getNome());
        assertEquals("Raccolta Siero", processoRecuperato2.getFasi().get(0).getNome());
    }

    @Test
    void testValidazioneBusinessLogic() {
        // Test creazione processo con nome duplicato
        processoService.creaProcesso("Processo Test", "Descrizione", trasformatore, null);

        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> processoService.creaProcesso("Processo Test", "Altra descrizione", trasformatore, null));
        assertTrue(exception.getMessage().contains("Esiste già un processo con il nome"));

        // Test aggiunta fase a processo inesistente
        FaseLavorazione fase = new FaseLavorazione(
                "Fase Test", "Descrizione", 1, "Materia prima", new FonteEsterna("Fornitore"));

        IllegalArgumentException exception2 = assertThrows(
                IllegalArgumentException.class,
                () -> processoService.aggiungiFaseAlProcesso(999L, fase));
        assertTrue(exception2.getMessage().contains("Processo con ID 999 non trovato"));
    }

    @Test
    void testTracciabilitaCompleta() {
        // Crea processo con tracciabilità completa
        ProcessoTrasformazione processo = processoService.creaProcesso(
                "Produzione Parmigiano Reggiano",
                "Processo DOP con tracciabilità completa",
                trasformatore,
                "Metodo tradizionale 24 mesi");

        // Fase con fonte interna tracciabile
        FaseLavorazione fase1 = new FaseLavorazione(
                "Mungitura Mattutina",
                "Raccolta latte delle ore 6:00",
                1,
                "Latte crudo",
                new FonteInterna(produttore));

        // Fase con fonte esterna tracciabile
        FaseLavorazione fase2 = new FaseLavorazione(
                "Aggiunta Siero Innesto",
                "Aggiunta siero della lavorazione precedente",
                2,
                "Siero innesto naturale",
                new FonteEsterna("Caseificio Cooperativo Emilia - Lotto #2024001"));

        processo = processoService.aggiungiFaseAlProcesso(processo.getId(), fase1);
        processo = processoService.aggiungiFaseAlProcesso(processo.getId(), fase2);

        // Converti a DTO e verifica tracciabilità
        ProcessoTrasformazioneDTO dto = mapper.toDto(processo);

        // Verifica tracciabilità fonte interna
        FaseLavorazioneDTO fase1DTO = dto.getFasi().get(0);
        assertTrue(fase1DTO.getFonteMateriaPrima().contains("Giuseppe Rossi"));
        assertTrue(fase1DTO.getFonteMateriaPrima().contains("Allevamento Valle Verde"));

        // Verifica tracciabilità fonte esterna
        FaseLavorazioneDTO fase2DTO = dto.getFasi().get(1);
        assertTrue(fase2DTO.getFonteMateriaPrima().contains("Caseificio Cooperativo Emilia"));
        assertTrue(fase2DTO.getFonteMateriaPrima().contains("Lotto #2024001"));
    }

    @Test
    void testPerformanceConMolteFasi() {
        // Test con processo complesso (molte fasi)
        ProcessoTrasformazione processo = processoService.creaProcesso(
                "Processo Complesso",
                "Processo con molte fasi per test performance",
                trasformatore,
                "Test");

        // Aggiungi 20 fasi
        for (int i = 1; i <= 20; i++) {
            FaseLavorazione fase = new FaseLavorazione(
                    "Fase " + i,
                    "Descrizione fase " + i,
                    i,
                    "Materia prima " + i,
                    i % 2 == 0 ? new FonteInterna(produttore) : new FonteEsterna("Fornitore " + i));
            processo = processoService.aggiungiFaseAlProcesso(processo.getId(), fase);
        }

        // Verifica che tutte le fasi siano state aggiunte
        assertEquals(20, processo.getFasi().size());

        // Test conversione DTO (dovrebbe essere veloce)
        long startTime = System.currentTimeMillis();
        ProcessoTrasformazioneDTO dto = mapper.toDto(processo);
        long endTime = System.currentTimeMillis();

        // Verifica risultato
        assertEquals(20, dto.getFasi().size());

        // Verifica che la conversione sia stata veloce (< 100ms)
        assertTrue((endTime - startTime) < 100, "Conversione DTO troppo lenta: " + (endTime - startTime) + "ms");

        // Verifica ordine corretto delle fasi
        for (int i = 0; i < 20; i++) {
            assertEquals(i + 1, dto.getFasi().get(i).getOrdineEsecuzione());
        }
    }
}