// package it.unicam.cs.ids.piattaforma_agricola_locale.service.impl;

// import static org.junit.jupiter.api.Assertions.*;

// import java.util.ArrayList;
// import java.util.List;

// import org.junit.jupiter.api.BeforeEach;
// import org.junit.jupiter.api.Test;

// import it.unicam.cs.ids.piattaforma_agricola_locale.model.catalogo.Prodotto;
// import it.unicam.cs.ids.piattaforma_agricola_locale.model.catalogo.TipoOrigineProdotto;
// import it.unicam.cs.ids.piattaforma_agricola_locale.model.repository.ProcessoTrasformazioneRepository;
// import it.unicam.cs.ids.piattaforma_agricola_locale.model.trasformazione.FaseLavorazione;
// import it.unicam.cs.ids.piattaforma_agricola_locale.model.trasformazione.InputLavorazione;
// import it.unicam.cs.ids.piattaforma_agricola_locale.model.trasformazione.ProcessoTrasformazione;
// import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.DatiAzienda;
// import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.TipoRuolo;
// import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.Trasformatore;
// import it.unicam.cs.ids.piattaforma_agricola_locale.service.interfaces.IProcessoTrasformazioneService;

// /**
//  * Test di integrazione per il sistema di gestione dei processi di
//  * trasformazione.
//  * Testa l'intero flusso dalla creazione dei processi alla gestione dei prodotti
//  * trasformati.
//  */
// class ProcessoTrasformazioneIntegrationTest {

//     private IProcessoTrasformazioneService processoService;

//     private ProcessoTrasformazioneRepository processoRepository;

//     private Trasformatore trasformatore;
//     private Prodotto latte;
//     private Prodotto caglio;
//     private Prodotto formaggio;

//     @BeforeEach
//     void setUp() {
//         // Inizializza repository e servizio
//         processoRepository = new ProcessoTrasformazioneRepository();
//         processoService = new ProcessoTrasformazioneService(processoRepository);

//         // Pulisce il repository prima di ogni test
//         processoRepository.pulisciTutti();

//         // Crea dati di test
//         DatiAzienda datiAzienda = new DatiAzienda(1, "Caseificio Test", "12345678901", "Via Test 123",
//                 "Descrizione test", "", "");

//         trasformatore = new Trasformatore(1, "Mario", "Rossi", "<EMAIL>", "password123",
//                 "123456789", datiAzienda,  TipoRuolo.TRASFORMATORE);

//         // Crea prodotti di base (materie prime)
//         latte = new Prodotto(1, "Latte fresco", "Latte di mucca", 1.50, 100, trasformatore);
//         caglio = new Prodotto(2, "Caglio", "Caglio naturale", 5.00, 20, trasformatore);

//         // Crea prodotto finale (che sarà risultato della trasformazione)
//         formaggio = new Prodotto(3, "Formaggio fresco", "Formaggio di latte vaccino", 8.50, 10, trasformatore);
//     }

//     @Test
//     void testCreazioneProcessoCompleto() {
//         // Crea il processo di trasformazione
//         ProcessoTrasformazione processo = processoService.creaProcesso(
//                 "Produzione Formaggio Fresco",
//                 "Processo per la produzione di formaggio fresco da latte vaccino",
//                 trasformatore,
//                 "Tradizionale");

//         assertNotNull(processo);
//         assertNotNull(processo.getId());
//         assertEquals("Produzione Formaggio Fresco", processo.getNome());
//         assertEquals(trasformatore, processo.getTrasformatore());
//         assertTrue(processo.isAttivo());

//         // Crea le fasi di lavorazione
//         FaseLavorazione riscaldamento = new FaseLavorazione(
//                 "Riscaldamento",
//                 "Riscaldamento del latte a 35°C",
//                 1, 30, true);

//         FaseLavorazione aggiuntaCaglio = new FaseLavorazione(
//                 "Aggiunta Caglio",
//                 "Aggiunta del caglio e coagulazione",
//                 2, 45, true);

//         FaseLavorazione taglio = new FaseLavorazione(
//                 "Taglio Cagliata",
//                 "Taglio della cagliata in piccoli pezzi",
//                 3, 20, true);

//         // Crea gli input per le fasi
//         InputLavorazione inputLatte = new InputLavorazione(latte, 10.0, "litri");
//         InputLavorazione inputCaglio = new InputLavorazione(caglio, 0.5, "grammi");

//         // Aggiunge input alle fasi
//         processoService.aggiungiInputAFase(riscaldamento, inputLatte);
//         processoService.aggiungiInputAFase(aggiuntaCaglio, inputCaglio);

//         // Aggiunge le fasi al processo
//         ProcessoTrasformazione processoAggiornato = processoService.aggiungiFaseAlProcesso(processo.getId(),
//                 riscaldamento);
//         processoAggiornato = processoService.aggiungiFaseAlProcesso(processo.getId(), aggiuntaCaglio);
//         processoAggiornato = processoService.aggiungiFaseAlProcesso(processo.getId(), taglio);

//         // Verifica il processo completo
//         assertEquals(3, processoAggiornato.getNumeroFasi());
//         assertTrue(processoAggiornato.isCompleto());
//         assertTrue(processoAggiornato.isEseguibile());

//         // Verifica le durate e costi
//         assertEquals(95, processoAggiornato.getDurataTotaleStimataMinuti()); // 30+45+20
//         assertTrue(processoAggiornato.getDurataTotaleStimataOre() > 1.5);

//         double costoAtteso = (10.0 * 1.50) + (0.5 * 5.00); // latte + caglio
//         assertEquals(costoAtteso, processoAggiornato.getCostoTotaleStimato(), 0.01);
//     }

//     @Test
//     void testGestioneProdottoTrasformato() {
//         // Crea e salva un processo
//         ProcessoTrasformazione processo = processoService.creaProcesso(
//                 "Produzione Test",
//                 "Processo di test",
//                 trasformatore,
//                 null);

//         // Aggiunge il prodotto trasformato al trasformatore
//         trasformatore.aggiungiProdottoTrasformato(formaggio, processo.getId());

//         // Verifica che il prodotto sia stato marcato come trasformato
//         assertTrue(formaggio.isTrasformato());
//         assertEquals(TipoOrigineProdotto.TRASFORMATO, formaggio.getTipoOrigine());
//         assertEquals(processo.getId(), formaggio.getIdProcessoTrasformazioneOriginario());

//         // Verifica le capacità del trasformatore
//         assertEquals(1, trasformatore.contaProdottiTrasformati());
//         assertTrue(trasformatore.offreProdottiTrasformati());

//         List<Prodotto> prodottiTrasformati = trasformatore.getProdottiTrasformati();
//         assertEquals(1, prodottiTrasformati.size());
//         assertTrue(prodottiTrasformati.contains(formaggio));
//     }

//     @Test
//     void testValidazioneProcesso() {
//         // Test processo valido
//         ProcessoTrasformazione processo = processoService.creaProcesso(
//                 "Processo Valido",
//                 "Descrizione processo",
//                 trasformatore,
//                 "Metodo test");

//         FaseLavorazione fase = new FaseLavorazione("Fase Test", "Descrizione fase", 1);
//         processoService.aggiungiFaseAlProcesso(processo.getId(), fase);

//         assertTrue(processoService.validaProcesso(processo.getId()));

//         // Test processo non valido (senza fasi)
//         ProcessoTrasformazione processoVuoto = processoService.creaProcesso(
//                 "Processo Vuoto",
//                 "Processo senza fasi",
//                 trasformatore,
//                 null);

//         assertFalse(processoService.validaProcesso(processoVuoto.getId()));
//     }

//     @Test
//     void testRicercaProcessi() {
//         // Crea diversi processi
//         ProcessoTrasformazione processo1 = processoService.creaProcesso(
//                 "Produzione Formaggio",
//                 "Formaggio fresco",
//                 trasformatore,
//                 "Tradizionale");

//         ProcessoTrasformazione processo2 = processoService.creaProcesso(
//                 "Produzione Yogurt",
//                 "Yogurt naturale",
//                 trasformatore,
//                 "Moderno");

//         ProcessoTrasformazione processo3 = processoService.creaProcesso(
//                 "Stagionatura Formaggio",
//                 "Formaggio stagionato",
//                 trasformatore,
//                 "Tradizionale");

//         // Verifica che i processi siano stati creati correttamente
//         assertNotNull(processo1);
//         assertNotNull(processo2);
//         assertNotNull(processo3);

//         // Test ricerca per nome
//         List<ProcessoTrasformazione> processiFormaggio = processoService.findByNome("Formaggio");
//         assertEquals(2, processiFormaggio.size());

//         // Test ricerca per metodo di produzione
//         List<ProcessoTrasformazione> processiTradizionali = processoService.findByMetodoProduzione("Tradizionale");
//         assertEquals(2, processiTradizionali.size());

//         List<ProcessoTrasformazione> processiModerni = processoService.findByMetodoProduzione("Moderno");
//         assertEquals(1, processiModerni.size());

//         List<ProcessoTrasformazione> tuttiProcessi = processoService.findByTrasformatore(trasformatore);
//         assertEquals(3, tuttiProcessi.size());
//     }

//     @Test
//     void testStatisticheProcessi() {
//         // Crea processi con diverse caratteristiche
//         ProcessoTrasformazione processo1 = processoService.creaProcesso(
//                 "Processo 1", "Descrizione 1", trasformatore, "Metodo1");

//         ProcessoTrasformazione processo2 = processoService.creaProcesso(
//                 "Processo 2", "Descrizione 2", trasformatore, "Metodo2");

//         // Verifica che i processi siano stati creati
//         assertNotNull(processo1);
//         assertNotNull(processo2);

//         // Aggiunge fasi con costi diversi
//         FaseLavorazione fase1 = new FaseLavorazione("Fase 1", "Descrizione", 1, 60, true);
//         InputLavorazione input1 = new InputLavorazione(latte, 5.0, "litri");
//         processoService.aggiungiInputAFase(fase1, input1);
//         processoService.aggiungiFaseAlProcesso(processo1.getId(), fase1);

//         FaseLavorazione fase2 = new FaseLavorazione("Fase 2", "Descrizione", 1, 120, true);
//         InputLavorazione input2 = new InputLavorazione(caglio, 2.0, "grammi");
//         processoService.aggiungiInputAFase(fase2, input2);
//         processoService.aggiungiFaseAlProcesso(processo2.getId(), fase2);

//         // Disattiva un processo
//         processoService.disattivaProcesso(processo2.getId());

//         // Ottiene statistiche
//         IProcessoTrasformazioneService.StatisticheProcessi statistiche = processoService
//                 .ottieniStatisticheProcessi(trasformatore);

//         assertEquals(2, statistiche.getNumeroTotaleProcessi());
//         assertEquals(1, statistiche.getNumeroProcessiAttivi());
//         assertTrue(statistiche.getCostoTotaleMedio() > 0);
//         assertTrue(statistiche.getDurataTotaleMedio() > 0);
//     }

//     @Test
//     void testEliminazioneProcesso() {
//         ProcessoTrasformazione processo = processoService.creaProcesso(
//                 "Processo da eliminare",
//                 "Processo test",
//                 trasformatore,
//                 null);

//         Long processoId = processo.getId();

//         // Verifica che il processo esista
//         assertTrue(processoService.findById(processoId).isPresent());

//         // Elimina il processo
//         boolean eliminato = processoService.eliminaProcesso(processoId, trasformatore);
//         assertTrue(eliminato);

//         // Verifica che il processo sia stato eliminato
//         assertFalse(processoService.findById(processoId).isPresent());
//     }

//     @Test
//     void testGestioneErrori() {
//         // Test creazione processo con parametri non validi
//         assertThrows(IllegalArgumentException.class,
//                 () -> processoService.creaProcesso(null, "Descrizione", trasformatore, null));

//         assertThrows(IllegalArgumentException.class,
//                 () -> processoService.creaProcesso("Nome", null, trasformatore, null));

//         assertThrows(IllegalArgumentException.class,
//                 () -> processoService.creaProcesso("Nome", "Descrizione", null, null));

//         // Test processo inesistente
//         assertThrows(IllegalArgumentException.class,
//                 () -> processoService.aggiungiFaseAlProcesso(999L, new FaseLavorazione("Test", "Test", 1)));

//         // Test eliminazione non autorizzata
//         ProcessoTrasformazione processo = processoService.creaProcesso(
//                 "Processo test", "Descrizione", trasformatore, null);

//         Trasformatore altroTrasformatore = new Trasformatore(2, "Luigi", "Verdi", "<EMAIL>",
//                 "password", "987654321",
//                 new DatiAzienda(2, "Altra Azienda", "98765432101", "Via Altra", "Altra descrizione", "", ""),
//                 TipoRuolo.TRASFORMATORE);

//         assertThrows(IllegalArgumentException.class,
//                 () -> processoService.eliminaProcesso(processo.getId(), altroTrasformatore));
//     }

//     @Test
//     void testTipoOrigineProdotto() {
//         // Test enum TipoOrigineProdotto
//         assertEquals("Coltivato/Allevato", TipoOrigineProdotto.COLTIVATO_ALLEVATO.getDescrizione());
//         assertEquals("Trasformato", TipoOrigineProdotto.TRASFORMATO.getDescrizione());

//         assertTrue(TipoOrigineProdotto.TRASFORMATO.isTrasformato());
//         assertFalse(TipoOrigineProdotto.TRASFORMATO.isColtivato());

//         assertTrue(TipoOrigineProdotto.COLTIVATO_ALLEVATO.isColtivato());
//         assertFalse(TipoOrigineProdotto.COLTIVATO_ALLEVATO.isTrasformato());
//     }
// }
