package it.unicam.cs.ids.piattaforma_agricola_locale.example;

import java.util.List;
import java.util.Optional;

import it.unicam.cs.ids.piattaforma_agricola_locale.model.catalogo.Prodotto;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.catalogo.TipoOrigineProdotto;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.repository.ProcessoTrasformazioneRepository;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.trasformazione.FaseLavorazione;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.trasformazione.FonteEsterna;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.trasformazione.FonteInterna;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.trasformazione.ProcessoTrasformazione;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.DatiAzienda;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.Produttore;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.TipoRuolo;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.Trasformatore;
import it.unicam.cs.ids.piattaforma_agricola_locale.service.impl.ProcessoTrasformazioneService;
import it.unicam.cs.ids.piattaforma_agricola_locale.service.interfaces.IProcessoTrasformazioneService;

/**
 * Classe di esempio che dimostra un possibile utilizzo completo del workflow
 * dei processi di trasformazione e tutto ciò che riguarda essi nella
 * piattaforma agricola Java Spring Boot.
 * 
 * Questa classe illustra:
 * - Creazione di trasformatori
 * - Gestione dei processi di trasformazione
 * - Configurazione delle fasi di lavorazione
 * - Utilizzo di fonti interne ed esterne
 * - Creazione di prodotti trasformati
 * - Ricerca e gestione dei processi
 * - Esempi di workflow completi di trasformazione
 */
public class EsempioProcessi {

    public static void main(String[] args) {
        System.out.println("🚀 ESEMPIO COMPLETO: WORKFLOW PROCESSI DI TRASFORMAZIONE");
        System.out.println("=".repeat(70));
        System.out.println();

        // Inizializza il sistema
        ProcessoTrasformazioneRepository repository = new ProcessoTrasformazioneRepository();
        IProcessoTrasformazioneService processoService = new ProcessoTrasformazioneService(repository);

        // Esempio 1: Workflow completo - Produzione Mozzarella
        esempioWorkflowCompleto(processoService, repository);

        System.out.println("\n" + "=".repeat(70) + "\n");

        // Esempio 2: Gestione avanzata processi
        esempioGestioneAvanzataProcessi(processoService, repository);

        System.out.println("\n" + "=".repeat(70) + "\n");

        // Esempio 3: Ricerca e filtraggio processi
        esempioRicercaFiltraggi(processoService);

        System.out.println("\n" + "=".repeat(70) + "\n");

        // Esempio 4: Statistiche e monitoraggio
        mostraStatisticheSistema(processoService, repository);

        System.out.println("\n🎯 ESEMPIO COMPLETATO CORRETTAMENTE!");
    }

    /**
     * Dimostra un workflow completo dalla creazione del trasformatore alla
     * produzione del prodotto finale.
     */
    private static void esempioWorkflowCompleto(IProcessoTrasformazioneService service,
            ProcessoTrasformazioneRepository repository) {
        System.out.println("📋 ESEMPIO 1: WORKFLOW COMPLETO - PRODUZIONE MOZZARELLA");
        System.out.println("-".repeat(60));

        // 1. CREAZIONE TRASFORMATORE
        System.out.println("👨‍🍳 Fase 1: Creazione del Trasformatore");
        DatiAzienda datiCaseificio = new DatiAzienda(
                1,
                "Caseificio La Bufala Siciliana",
                "09876543210",
                "Via Etnea 100, Catania",
                "Caseificio specializzato in mozzarella di bufala e prodotti lattiero-caseari tradizionali siciliani",
                "",
                "");

        Trasformatore caseificio = new Trasformatore(
                1,
                "Marco",
                "Palumbo",
                "<EMAIL>",
                "password123",
                "095123456",
                datiCaseificio,
                TipoRuolo.TRASFORMATORE);

        System.out.println("   ✅ Trasformatore creato: " + caseificio.getNome() + " " + caseificio.getCognome());
        System.out.println("   🏢 Azienda: " + datiCaseificio.getNomeAzienda());
        System.out.println("   📍 Indirizzo: " + datiCaseificio.getIndirizzoAzienda());

        // 2. CREAZIONE PRODOTTI BASE (MATERIE PRIME)
        System.out.println("\n🥛 Fase 2: Preparazione Materie Prime");
        Prodotto latteBufala = new Prodotto(1, "Latte di Bufala Fresco",
                "Latte di bufala proveniente da allevamenti selezionati dell'Etna",
                2.80, 50, caseificio);

        Prodotto caglio = new Prodotto(2, "Caglio Naturale",
                "Caglio di vitello per coagulazione naturale",
                12.50, 25, caseificio);

        Prodotto sale = new Prodotto(3, "Sale Marino di Trapani",
                "Sale marino integrale di Trapani IGP",
                1.20, 100, caseificio);

        Prodotto acqua = new Prodotto(4, "Acqua di Fonte",
                "Acqua di fonte dell'Etna, purissima e oligominerale",
                0.80, 200, caseificio);

        System.out.println("   • " + latteBufala.getNome() + " - €" + latteBufala.getPrezzo() + "/litro");
        System.out.println("   • " + caglio.getNome() + " - €" + caglio.getPrezzo() + "/100g");
        System.out.println("   • " + sale.getNome() + " - €" + sale.getPrezzo() + "/kg");
        System.out.println("   • " + acqua.getNome() + " - €" + acqua.getPrezzo() + "/litro");

        // 3. CREAZIONE PROCESSO DI TRASFORMAZIONE
        System.out.println("\n🔄 Fase 3: Creazione Processo di Trasformazione");
        ProcessoTrasformazione processoMozzarella = service.creaProcesso(
                "Produzione Mozzarella di Bufala Siciliana",
                "Processo tradizionale per la produzione di mozzarella di bufala secondo ricetta siciliana con latte dell'Etna",
                caseificio,
                "Metodo Tradizionale Siciliano DOP");

        System.out.println("   ✅ Processo creato: " + processoMozzarella.getNome());
        System.out.println("   🆔 ID Processo: " + processoMozzarella.getId());
        System.out.println("   🏷️ Metodo: " + processoMozzarella.getMetodoProduzione());

        // 4. CREAZIONE E CONFIGURAZIONE FASI DI LAVORAZIONE
        System.out.println("\n⚙️ Fase 4: Configurazione Fasi di Lavorazione");

        // Creazione delle fonti per le fasi
        // FonteInterna richiede un oggetto Produttore, non Prodotto
        Produttore produttoreLatte = new Produttore(10, "Giuseppe", "Siciliano", "<EMAIL>", "pass123",
                "095111222", datiCaseificio, TipoRuolo.PRODUTTORE);
        FonteInterna fonteInterna = new FonteInterna(produttoreLatte);
        // FonteEsterna richiede una stringa nomeFornitore, non Prodotto
        FonteEsterna fonteEsterna = new FonteEsterna("Fornitore Caglio Tradizionale");

        // Fase 1: Controllo Qualità
        FaseLavorazione controlloQualita = new FaseLavorazione(
                "Controllo Qualità Latte",
                "Verifica della qualità del latte: acidità, temperatura, assenza contaminanti",
                1,
                "Latte di Bufala",
                fonteInterna);

        // Fase 2: Riscaldamento
        FaseLavorazione riscaldamento = new FaseLavorazione(
                "Riscaldamento Controllato",
                "Riscaldamento graduale del latte a 37°C per preparazione alla coagulazione",
                2,
                "Latte di Bufala",
                fonteInterna);

        // Fase 3: Coagulazione
        FaseLavorazione coagulazione = new FaseLavorazione(
                "Coagulazione con Caglio",
                "Aggiunta del caglio naturale e formazione della cagliata",
                3,
                "Caglio Naturale",
                fonteEsterna);

        // Fase 4: Taglio Cagliata
        FaseLavorazione taglioCagliata = new FaseLavorazione(
                "Taglio della Cagliata",
                "Taglio della cagliata in grani di dimensione uniforme (8-10mm)",
                4,
                "Cagliata",
                fonteInterna);

        // Fase 5: Cottura
        FaseLavorazione cottura = new FaseLavorazione(
                "Cottura in Siero",
                "Riscaldamento della cagliata in siero a 45°C per espulsione siero",
                5,
                "Cagliata",
                fonteInterna);

        // Fase 6: Formatura
        FonteEsterna fonteSale = new FonteEsterna("Fornitore Sale Marino Trapani");
        FaseLavorazione formatura = new FaseLavorazione(
                "Formatura Mozzarella",
                "Lavorazione manuale della pasta per formazione delle mozzarelle",
                6,
                "Sale Marino",
                fonteSale);

        System.out.println("   📝 Fasi di lavorazione configurate:");
        FaseLavorazione[] fasiMozzarella = {
                controlloQualita, riscaldamento, coagulazione,
                taglioCagliata, cottura, formatura
        };

        for (FaseLavorazione fase : fasiMozzarella) {
            System.out.println("     " + fase.getOrdineEsecuzione() + ". " + fase.getNome());
        }

        // 5. AGGIUNTA FASI AL PROCESSO
        System.out.println("\n🔗 Fase 5: Collegamento Fasi al Processo");
        for (FaseLavorazione fase : fasiMozzarella) {
            service.aggiungiFaseAlProcesso(processoMozzarella.getId(), fase);
        }

        // 6. CREAZIONE PRODOTTO FINALE
        System.out.println("\n🧀 Fase 6: Creazione Prodotto Finale");
        Prodotto mozzarellaBufala = new Prodotto(
                5,
                "Mozzarella di Bufala Siciliana DOP",
                "Mozzarella di bufala prodotta con metodo tradizionale siciliano",
                15.80,
                0,
                caseificio,
                processoMozzarella.getId());

        // Registra il prodotto come trasformato
        mozzarellaBufala.setTipoOrigine(TipoOrigineProdotto.TRASFORMATO);
        mozzarellaBufala.setIdProcessoTrasformazioneOriginario(processoMozzarella.getId());
        caseificio.aggiungiProdottoOfferto(mozzarellaBufala);

        System.out.println("   ✅ Prodotto finale creato: " + mozzarellaBufala.getNome());
        System.out.println("   💰 Prezzo: €" + mozzarellaBufala.getPrezzo() + "/pezzo");
        System.out.println("   🏷️ Tipo origine: " + mozzarellaBufala.getTipoOrigine().getDescrizione());

        // 7. VERIFICA PROCESSO COMPLETO
        Optional<ProcessoTrasformazione> processoCompleto = repository.findById(processoMozzarella.getId());
        if (processoCompleto.isPresent()) {
            ProcessoTrasformazione processo = processoCompleto.get();
            System.out.println("\n📊 Fase 7: Riepilogo Processo Completo");
            System.out.println("   📈 Numero fasi: " + processo.getNumeroFasi());
            System.out.println("   ✅ Processo creato con successo");
        }
    }

    /**
     * Dimostra funzionalità avanzate di gestione dei processi.
     */
    private static void esempioGestioneAvanzataProcessi(IProcessoTrasformazioneService service,
            ProcessoTrasformazioneRepository repository) {
        System.out.println("🔧 ESEMPIO 2: GESTIONE AVANZATA PROCESSI");
        System.out.println("-".repeat(60));

        // Creazione secondo trasformatore per dimostrare multi-utente
        DatiAzienda datiOleificio = new DatiAzienda(
                2,
                "Oleificio Etna Verde",
                "11223344556",
                "Contrada Solicchiata, Castiglione di Sicilia",
                "Oleificio specializzato nella produzione di olio extravergine dell'Etna",
                "",
                "");

        Trasformatore oleificio = new Trasformatore(
                2,
                "Giuseppe",
                "Torrisi",
                "<EMAIL>",
                "password456",
                "095987654",
                datiOleificio,
                TipoRuolo.TRASFORMATORE);

        // Creazione di più processi per dimostrare la gestione
        System.out.println("🏭 Creazione Processi Multipli:");

        ProcessoTrasformazione processoOlioClassico = service.creaProcesso(
                "Produzione Olio EVO Classico",
                "Estrazione olio extravergine con metodo tradizionale a freddo",
                oleificio,
                "Spremitura a Freddo");

        ProcessoTrasformazione processoOlioPremium = service.creaProcesso(
                "Produzione Olio EVO Premium",
                "Estrazione olio extravergine da olive selezionate con metodo DOP",
                oleificio,
                "DOP Etna");

        ProcessoTrasformazione processoOlioBio = service.creaProcesso(
                "Produzione Olio EVO Biologico",
                "Estrazione olio extravergine da agricoltura biologica certificata",
                oleificio,
                "Biologico Certificato");

        System.out.println("   ✅ " + processoOlioClassico.getNome() + " (ID: " + processoOlioClassico.getId() + ")");
        System.out.println("   ✅ " + processoOlioPremium.getNome() + " (ID: " + processoOlioPremium.getId() + ")");
        System.out.println("   ✅ " + processoOlioBio.getNome() + " (ID: " + processoOlioBio.getId() + ")");

        // Aggiunta di fasi ai processi
        System.out.println("\n⚙️ Configurazione Fasi per Processo Premium:");

        // Crea produttori per le fonti
        Produttore produttoreOlive = new Produttore(11, "Marco", "Olivicoltore", "<EMAIL>", "pass456",
                "095333444", datiOleificio, TipoRuolo.PRODUTTORE);
        FonteInterna fonteOlive = new FonteInterna(produttoreOlive);
        Produttore produttoreAcqua = new Produttore(12, "Anna", "Fornitori", "<EMAIL>", "pass789", "095555666",
                datiOleificio, TipoRuolo.PRODUTTORE);
        FonteInterna fonteAcqua = new FonteInterna(produttoreAcqua);

        FaseLavorazione selezioneOlive = new FaseLavorazione(
                "Selezione Olive Premium",
                "Selezione manuale delle olive migliori per qualità DOP",
                1,
                "Olive Nocellara Etnea",
                fonteOlive);

        FaseLavorazione lavaggio = new FaseLavorazione(
                "Lavaggio Olive",
                "Lavaggio accurato delle olive con acqua fredda",
                2,
                "Acqua per Lavaggio",
                fonteAcqua);

        FaseLavorazione molatura = new FaseLavorazione(
                "Molatura Tradizionale",
                "Molatura delle olive con macine di pietra tradizionali",
                3,
                "Olive Lavate",
                fonteOlive);

        service.aggiungiFaseAlProcesso(processoOlioPremium.getId(), selezioneOlive);
        service.aggiungiFaseAlProcesso(processoOlioPremium.getId(), lavaggio);
        service.aggiungiFaseAlProcesso(processoOlioPremium.getId(), molatura);

        System.out.println("   📝 Fasi aggiunte al processo premium:");
        List<FaseLavorazione> fasiPremium = repository.findById(processoOlioPremium.getId()).get().getFasi();
        for (FaseLavorazione fase : fasiPremium) {
            System.out.println("     " + fase.getOrdineEsecuzione() + ". " + fase.getNome());
        }

        // Modifica di un processo esistente
        System.out.println("\n🔄 Modifica Processo Esistente:");
        processoOlioBio.setNote("Processo certificato ICEA per produzione biologica");
        ProcessoTrasformazione processoAggiornato = service.aggiornaProcesso(processoOlioBio);
        System.out.println("   ✅ Note aggiunte al processo biologico: " + processoAggiornato.getNote());

        // Rimozione di una fase
        System.out.println("\n❌ Rimozione Fase da Processo:");
        service.rimuoviFaseDalProcesso(processoOlioPremium.getId(), lavaggio);
        List<FaseLavorazione> fasiDopoRimozione = repository.findById(processoOlioPremium.getId()).get().getFasi();
        System.out.println("   📝 Fasi rimanenti dopo rimozione del lavaggio:");
        for (FaseLavorazione fase : fasiDopoRimozione) {
            System.out.println("     " + fase.getOrdineEsecuzione() + ". " + fase.getNome());
        }
    }

    /**
     * Dimostra le funzionalità di ricerca e filtraggio dei processi.
     */
    private static void esempioRicercaFiltraggi(IProcessoTrasformazioneService service) {
        System.out.println("🔍 ESEMPIO 3: RICERCA E FILTRAGGIO PROCESSI");
        System.out.println("-".repeat(60));

        // Ottieni il repository per le operazioni di ricerca
        ProcessoTrasformazioneRepository repository = new ProcessoTrasformazioneRepository();
        List<ProcessoTrasformazione> tuttiProcessi = repository.findAll();
        System.out.println("📊 Processi totali nel sistema: " + tuttiProcessi.size());

        // Ricerca per nome
        System.out.println("\n🔤 Ricerca per Nome:");
        List<ProcessoTrasformazione> processiMozzarella = repository.findByNome("Mozzarella");
        System.out.println("   Processi contenenti 'Mozzarella': " + processiMozzarella.size());
        for (ProcessoTrasformazione processo : processiMozzarella) {
            System.out.println("     • " + processo.getNome());
        }

        List<ProcessoTrasformazione> processiOlio = repository.findByNome("Olio");
        System.out.println("   Processi contenenti 'Olio': " + processiOlio.size());
        for (ProcessoTrasformazione processo : processiOlio) {
            System.out.println("     • " + processo.getNome());
        }

        // Ricerca per metodo di produzione
        System.out.println("\n🏷️ Ricerca per Metodo di Produzione:");
        List<ProcessoTrasformazione> processiTradizionali = repository
                .findByMetodoProduzione("Metodo Tradizionale Siciliano DOP");
        System.out.println("   Processi 'Tradizionali Siciliani': " + processiTradizionali.size());
        for (ProcessoTrasformazione processo : processiTradizionali) {
            System.out.println("     • " + processo.getNome() + " (Trasformatore: " +
                    processo.getTrasformatore().getNome() + ")");
        }

        List<ProcessoTrasformazione> processiBio = repository.findByMetodoProduzione("Biologico Certificato");
        System.out.println("   Processi 'Biologici': " + processiBio.size());
        for (ProcessoTrasformazione processo : processiBio) {
            System.out.println("     • " + processo.getNome());
        }

        // Ricerca per trasformatore
        if (!tuttiProcessi.isEmpty()) {
            Trasformatore primoTrasformatore = tuttiProcessi.get(0).getTrasformatore();
            System.out.println("\n👨‍🍳 Ricerca per Trasformatore:");
            List<ProcessoTrasformazione> processiTrasformatore = repository.findByTrasformatore(primoTrasformatore);
            System.out.println("   Processi di " + primoTrasformatore.getNome() + ": " + processiTrasformatore.size());
            for (ProcessoTrasformazione processo : processiTrasformatore) {
                System.out.println("     • " + processo.getNome());
            }

            // Ricerca processi attivi del trasformatore
            List<ProcessoTrasformazione> processiAttivi = repository.findActiveByTrasformatore(primoTrasformatore);
            System.out.println("   Processi attivi di " + primoTrasformatore.getNome() + ": " + processiAttivi.size());
        }

        // Ricerca per ID specifico
        if (!tuttiProcessi.isEmpty()) {
            Long idRicerca = tuttiProcessi.get(0).getId();
            System.out.println("\n🆔 Ricerca per ID Specifico:");
            Optional<ProcessoTrasformazione> processoTrovato = repository.findById(idRicerca);
            if (processoTrovato.isPresent()) {
                ProcessoTrasformazione processo = processoTrovato.get();
                System.out.println("   ✅ Processo trovato: " + processo.getNome());
                System.out.println("     Descrizione: " + processo.getDescrizione());
                System.out.println("     Fasi: " + processo.getNumeroFasi());
                System.out.println("     Trasformatore: " + processo.getTrasformatore().getNome());
            }
        }
    }

    /**
     * Mostra statistiche e informazioni di monitoraggio del sistema.
     */
    private static void mostraStatisticheSistema(IProcessoTrasformazioneService service,
            ProcessoTrasformazioneRepository repository) {
        System.out.println("📈 ESEMPIO 4: STATISTICHE E MONITORAGGIO SISTEMA");
        System.out.println("-".repeat(60));

        // Statistiche generali
        List<ProcessoTrasformazione> tuttiProcessi = repository.findAll();
        // Nota: Il metodo findActive() non è disponibile nel repository, usiamo
        // findAll()
        List<ProcessoTrasformazione> processiAttivi = repository.findAll(); // Tutti i processi sono considerati attivi

        System.out.println("📊 Statistiche Generali:");
        System.out.println("   • Totale processi nel sistema: " + tuttiProcessi.size());
        System.out.println("   • Processi disponibili: " + processiAttivi.size());
        System.out.println("   • Sistema in funzione correttamente");

        if (!tuttiProcessi.isEmpty()) {
            // Calcolo medie sui dati disponibili
            int faseTotaliMedie = (int) tuttiProcessi.stream()
                    .mapToInt(ProcessoTrasformazione::getNumeroFasi)
                    .average()
                    .orElse(0.0);

            System.out.println("\n📊 Medie di Sistema:");
            System.out.println("   • Numero medio fasi: " + faseTotaliMedie);
            System.out.println("   • Sistema di trasformazione attivo");

            // Processo più complesso
            ProcessoTrasformazione processoComplesso = tuttiProcessi.stream()
                    .max((p1, p2) -> Integer.compare(p1.getNumeroFasi(), p2.getNumeroFasi()))
                    .orElse(null);

            if (processoComplesso != null) {
                System.out.println("\n🏆 Processo Più Complesso:");
                System.out.println("   • Nome: " + processoComplesso.getNome());
                System.out.println("   • Fasi: " + processoComplesso.getNumeroFasi());
                System.out.println("   • Trasformatore: " + processoComplesso.getTrasformatore().getNome());
            }

            // Distribuzione per metodo di produzione
            System.out.println("\n📋 Distribuzione per Metodo di Produzione:");
            tuttiProcessi.stream()
                    .map(ProcessoTrasformazione::getMetodoProduzione)
                    .filter(metodo -> metodo != null && !metodo.isEmpty())
                    .distinct()
                    .forEach(metodo -> {
                        long count = tuttiProcessi.stream()
                                .filter(p -> metodo.equals(p.getMetodoProduzione()))
                                .count();
                        System.out.println("   • " + metodo + ": " + count + " processi");
                    });
        }

        // Informazioni sui trasformatori
        System.out.println("\n👥 Trasformatori nel Sistema:");
        tuttiProcessi.stream()
                .map(ProcessoTrasformazione::getTrasformatore)
                .distinct()
                .forEach(trasformatore -> {
                    long processiTrasformatore = tuttiProcessi.stream()
                            .filter(p -> trasformatore.equals(p.getTrasformatore()))
                            .count();
                    System.out.println("   • " + trasformatore.getNome() + " " + trasformatore.getCognome() +
                            " (" + trasformatore.getDatiAzienda().getNomeAzienda() + "): " +
                            processiTrasformatore + " processi");
                });

        System.out.println("\n✅ Sistema funzionante correttamente!");
        System.out.println("   📋 Tutti i workflow dei processi di trasformazione sono operativi");
        System.out.println("   🔧 Servizi di gestione, ricerca e monitoraggio attivi");
        System.out.println("   📊 Raccolta dati e statistiche in tempo reale");
    }
}
