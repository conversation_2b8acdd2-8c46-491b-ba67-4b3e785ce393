// package it.unicam.cs.ids.piattaforma_agricola_locale.example;

// import java.util.ArrayList;
// import java.util.ArrayList;
// import java.util.List;

// import it.unicam.cs.ids.piattaforma_agricola_locale.model.catalogo.Prodotto;
// import it.unicam.cs.ids.piattaforma_agricola_locale.model.repository.ProcessoTrasformazioneRepository;
// import it.unicam.cs.ids.piattaforma_agricola_locale.model.trasformazione.FaseLavorazione;
// import it.unicam.cs.ids.piattaforma_agricola_locale.model.trasformazione.InputLavorazione;
// import it.unicam.cs.ids.piattaforma_agricola_locale.model.trasformazione.ProcessoTrasformazione;
// import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.DatiAzienda;
// import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.TipoRuolo;
// import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.Trasformatore;
// import it.unicam.cs.ids.piattaforma_agricola_locale.service.impl.ProcessoTrasformazioneService;
// import it.unicam.cs.ids.piattaforma_agricola_locale.service.interfaces.IProcessoTrasformazioneService;

// /**
//  * Classe di esempio che dimostra l'utilizzo completo del sistema di
//  * trasformazione.
//  * Mostra come creare trasformatori, processi di trasformazione e gestire la
//  * tracciabilità.
//  */
// public class EsempioSistemaTrasformazione {

//     public static void main(String[] args) {
//         // Inizializza il sistema
//         ProcessoTrasformazioneRepository repository = new ProcessoTrasformazioneRepository();
//         IProcessoTrasformazioneService service = new ProcessoTrasformazioneService(repository);

//         System.out.println("=== Sistema di Gestione Trasformazione Agricola ===\n");

//         // Esempio 1: Caseificio - Produzione di Formaggio
//         esempioProduzioneFormaggio(service);

//         System.out.println("\n" + "=".repeat(60) + "\n");

//         // Esempio 2: Oleificio - Produzione di Olio d'Oliva
//         esempioProduzioneOlio(service);

//         System.out.println("\n" + "=".repeat(60) + "\n");

//         // Esempio 3: Panificio - Produzione di Pane
//         esempioProduzionePane(service);

//         System.out.println("\n" + "=".repeat(60) + "\n");

//         // Mostra statistiche finali
//         mostraStatisticheSistema(service, repository);
//     }

//     private static void esempioProduzioneFormaggio(IProcessoTrasformazioneService service) {
//         System.out.println("📧 ESEMPIO 1: CASEIFICIO - Produzione di Formaggio Fresco");
//         System.out.println("-".repeat(50));

//         // Crea il trasformatore (caseificio)
//         DatiAzienda datiCaseificio = new DatiAzienda(1, "Caseificio Delle Madonie",
//                 "12345678901", "Via Roma 123, Palermo", "Caseificio tradizionale siciliano", "", "");

//         Trasformatore caseificio = new Trasformatore(1, "Giuseppe", "Bianchi",
//                 "<EMAIL>", "password123", "091123456",
//                 datiCaseificio,  TipoRuolo.TRASFORMATORE);

//         // Crea le materie prime
//         Prodotto latte = new Prodotto(1, "Latte Fresco di Mucca",
//                 "Latte fresco appena munto", 1.80, 500, caseificio);

//         Prodotto caglio = new Prodotto(2, "Caglio Naturale",
//                 "Caglio estratto da vitello", 8.50, 50, caseificio);

//         Prodotto sale = new Prodotto(3, "Sale Marino",
//                 "Sale marino integrale", 2.00, 100, caseificio);

//         System.out.println("✅ Materie prime create:");
//         System.out.println("   • " + latte.getNome() + " - €" + latte.getPrezzo() + "/litro");
//         System.out.println("   • " + caglio.getNome() + " - €" + caglio.getPrezzo() + "/100g");
//         System.out.println("   • " + sale.getNome() + " - €" + sale.getPrezzo() + "/kg");

//         // Crea il processo di trasformazione
//         ProcessoTrasformazione processoFormaggio = service.creaProcesso(
//                 "Produzione Formaggio Fresco Siciliano",
//                 "Processo tradizionale per la produzione di formaggio fresco secondo ricetta siciliana",
//                 caseificio,
//                 "Metodo Tradizionale Siciliano");

//         System.out.println("\n🔄 Processo di trasformazione creato: " + processoFormaggio.getNome());

//         // Crea le fasi di lavorazione
//         FaseLavorazione[] fasi = {
//                 new FaseLavorazione("Riscaldamento Latte",
//                         "Riscaldamento del latte a 37°C per attivazione fermentazione", 1, 20, true),
//                 new FaseLavorazione("Aggiunta Caglio",
//                         "Aggiunta del caglio e mescolamento per coagulazione", 2, 30, true),
//                 new FaseLavorazione("Coagulazione",
//                         "Attesa per la formazione della cagliata", 3, 60, true),
//                 new FaseLavorazione("Taglio Cagliata",
//                         "Taglio della cagliata in cubetti uniformi", 4, 15, true),
//                 new FaseLavorazione("Spurgo Siero",
//                         "Separazione del siero dalla cagliata", 5, 45, true),
//                 new FaseLavorazione("Salatura",
//                         "Aggiunta del sale e mescolamento", 6, 10, true),
//                 new FaseLavorazione("Pressatura",
//                         "Pressatura per eliminare siero residuo", 7, 120, false),
//                 new FaseLavorazione("Confezionamento",
//                         "Confezionamento del formaggio fresco", 8, 15, true)
//         };

//         // Crea gli input per le fasi
//         InputLavorazione inputLatte = new InputLavorazione(latte, 20.0, "litri",
//                 "Latte fresco di giornata");
//         InputLavorazione inputCaglio = new InputLavorazione(caglio, 2.0, "grammi",
//                 "Caglio naturale di vitello");
//         InputLavorazione inputSale = new InputLavorazione(sale, 0.4, "kg",
//                 "Sale marino integrale");

//         // Aggiunge input alle fasi appropriate
//         service.aggiungiInputAFase(fasi[0], inputLatte);
//         service.aggiungiInputAFase(fasi[1], inputCaglio);
//         service.aggiungiInputAFase(fasi[5], inputSale);

//         // Aggiunge tutte le fasi al processo
//         for (FaseLavorazione fase : fasi) {
//             service.aggiungiFaseAlProcesso(processoFormaggio.getId(), fase);
//         }

//         System.out.println("📋 Fasi di lavorazione aggiunte: " + fasi.length);

//         // Crea il prodotto finale
//         Prodotto formaggioFresco = new Prodotto(4, "Formaggio Fresco Siciliano",
//                 "Formaggio fresco prodotto con metodo tradizionale siciliano",
//                 12.50, 0, caseificio, processoFormaggio.getId());

//         // Aggiunge il prodotto trasformato al caseificio
//         caseificio.aggiungiProdottoTrasformato(formaggioFresco, processoFormaggio.getId());

//         // Mostra informazioni complete
//         ProcessoTrasformazione processoCompleto = service.findById(processoFormaggio.getId()).get();

//         System.out.println("\n📊 RISULTATI PROCESSO:");
//         System.out.println("   ⏱️  Durata totale: "
//                 + String.format("%.1f", processoCompleto.getDurataTotaleStimataOre()) + " ore");
//         System.out.println(
//                 "   💰 Costo materie prime: €" + String.format("%.2f", processoCompleto.getCostoTotaleStimato()));
//         System.out
//                 .println("   🧀 Prodotto finale: " + formaggioFresco.getNome() + " - €" + formaggioFresco.getPrezzo());
//         System.out.println("   🏷️  Tipo origine: " + formaggioFresco.getTipoOrigine().getDescrizione());
//         System.out.println("   ✅ Processo eseguibile: " + (processoCompleto.isEseguibile() ? "Sì" : "No"));

//         System.out.println("\n" + caseificio.getDescrizioneCapacita());
//     }

//     private static void esempioProduzioneOlio(IProcessoTrasformazioneService service) {
//         System.out.println("🫒 ESEMPIO 2: OLEIFICIO - Produzione di Olio Extravergine");
//         System.out.println("-".repeat(50));

//         // Crea il trasformatore (oleificio)
//         DatiAzienda datiOleificio = new DatiAzienda(2, "Oleificio Monti Iblei",
//                 "23456789012", "Strada Provinciale 25, Ragusa", "Oleificio tradizionale siciliano", "", "");

//         Trasformatore oleificio = new Trasformatore(2, "Salvatore", "Greco",
//                 "<EMAIL>", "password456", "0932987654",
//                 datiOleificio,  TipoRuolo.TRASFORMATORE);

//         // Crea le materie prime
//         Prodotto olive = new Prodotto(5, "Olive Nocellara Etnea",
//                 "Olive varietà nocellara coltivate sull'Etna", 3.50, 1000, oleificio);

//         System.out.println("✅ Materia prima:");
//         System.out.println("   • " + olive.getNome() + " - €" + olive.getPrezzo() + "/kg");

//         // Crea il processo di trasformazione
//         ProcessoTrasformazione processoOlio = service.creaProcesso(
//                 "Estrazione Olio Extravergine a Freddo",
//                 "Processo di estrazione dell'olio extravergine di oliva a freddo",
//                 oleificio,
//                 "Spremitura a Freddo");

//         System.out.println("\n🔄 Processo di trasformazione creato: " + processoOlio.getNome());

//         // Crea le fasi di lavorazione
//         FaseLavorazione[] fasiOlio = {
//                 new FaseLavorazione("Lavaggio Olive",
//                         "Lavaggio delle olive con acqua corrente", 1, 30, true),
//                 new FaseLavorazione("Molitura",
//                         "Frantumazione delle olive con molino a martelli", 2, 45, true),
//                 new FaseLavorazione("Gramolazione",
//                         "Rimescolamento della pasta di olive", 3, 90, true),
//                 new FaseLavorazione("Estrazione Centrifuga",
//                         "Separazione olio-acqua mediante centrifugazione", 4, 60, true),
//                 new FaseLavorazione("Separazione Finale",
//                         "Separazione finale dell'olio dall'acqua", 5, 30, true),
//                 new FaseLavorazione("Filtrazione",
//                         "Filtrazione dell'olio per rimozione impurità", 6, 20, false),
//                 new FaseLavorazione("Stoccaggio",
//                         "Conservazione in contenitori di acciaio inox", 7, 15, true)
//         };

//         // Crea input
//         InputLavorazione inputOlive = new InputLavorazione(olive, 100.0, "kg",
//                 "Olive fresche raccolte da max 24 ore");

//         service.aggiungiInputAFase(fasiOlio[0], inputOlive);

//         // Aggiunge fasi al processo
//         for (FaseLavorazione fase : fasiOlio) {
//             service.aggiungiFaseAlProcesso(processoOlio.getId(), fase);
//         }

//         // Crea prodotto finale
//         Prodotto olioEVO = new Prodotto(6, "Olio Extravergine Etna DOP",
//                 "Olio extravergine di oliva Nocellara Etnea DOP",
//                 18.00, 0, oleificio, processoOlio.getId());

//         oleificio.aggiungiProdottoTrasformato(olioEVO, processoOlio.getId());

//         ProcessoTrasformazione processoOlioCompleto = service.findById(processoOlio.getId()).get();

//         System.out.println("\n📊 RISULTATI PROCESSO:");
//         System.out.println("   ⏱️  Durata totale: "
//                 + String.format("%.1f", processoOlioCompleto.getDurataTotaleStimataOre()) + " ore");
//         System.out.println(
//                 "   💰 Costo materie prime: €" + String.format("%.2f", processoOlioCompleto.getCostoTotaleStimato()));
//         System.out.println("   🫒 Prodotto finale: " + olioEVO.getNome() + " - €" + olioEVO.getPrezzo() + "/litro");
//         System.out.println("   🏷️  Tipo origine: " + olioEVO.getTipoOrigine().getDescrizione());

//         System.out.println("\n" + oleificio.getDescrizioneCapacita());
//     }

//     private static void esempioProduzionePane(IProcessoTrasformazioneService service) {
//         System.out.println("🍞 ESEMPIO 3: PANIFICIO - Produzione di Pane Tradizionale");
//         System.out.println("-".repeat(50));

//         // Crea il trasformatore (panificio)
//         DatiAzienda datiPanificio = new DatiAzienda(3, "Antico Forno Siciliano",
//                 "34567890123", "Via Garibaldi 45, Catania", "Panificio tradizionale siciliano", "", "");

//         Trasformatore panificio = new Trasformatore(3, "Antonio", "Russo",
//                 "<EMAIL>", "password789", "095567890",
//                 datiPanificio,  TipoRuolo.TRASFORMATORE);

//         // Crea le materie prime
//         Prodotto grano = new Prodotto(7, "Farina di Grano Duro Timilia",
//                 "Farina integrale di grano duro varietà timilia", 2.80, 200, panificio);

//         Prodotto lievito = new Prodotto(8, "Lievito Madre",
//                 "Lievito madre naturale rinfrescato", 5.00, 10, panificio);

//         Prodotto acqua = new Prodotto(9, "Acqua di Sorgente",
//                 "Acqua di sorgente dell'Etna", 0.50, 1000, panificio);

//         Prodotto salePane = new Prodotto(10, "Sale Marino di Trapani",
//                 "Sale marino integrale di Trapani", 1.50, 50, panificio);

//         System.out.println("✅ Materie prime create:");
//         System.out.println("   • " + grano.getNome() + " - €" + grano.getPrezzo() + "/kg");
//         System.out.println("   • " + lievito.getNome() + " - €" + lievito.getPrezzo() + "/100g");
//         System.out.println("   • " + acqua.getNome() + " - €" + acqua.getPrezzo() + "/litro");
//         System.out.println("   • " + salePane.getNome() + " - €" + salePane.getPrezzo() + "/kg");

//         // Crea il processo
//         ProcessoTrasformazione processoPane = service.creaProcesso(
//                 "Produzione Pane Tradizionale Siciliano",
//                 "Processo tradizionale per la produzione di pane con lievito madre",
//                 panificio,
//                 "Lievitazione Naturale");

//         System.out.println("\n🔄 Processo di trasformazione creato: " + processoPane.getNome());

//         // Fasi di lavorazione
//         FaseLavorazione[] fasiPane = {
//                 new FaseLavorazione("Rinfresco Lievito Madre",
//                         "Rinfresco del lievito madre con farina e acqua", 1, 30, true),
//                 new FaseLavorazione("Preparazione Impasto",
//                         "Impastamento di farina, acqua, lievito e sale", 2, 45, true),
//                 new FaseLavorazione("Prima Lievitazione",
//                         "Prima lievitazione dell'impasto", 3, 180, true),
//                 new FaseLavorazione("Pieghe di Rinforzo",
//                         "Pieghe per rinforzare la maglia glutinica", 4, 20, true),
//                 new FaseLavorazione("Seconda Lievitazione",
//                         "Seconda lievitazione in forma", 5, 120, true),
//                 new FaseLavorazione("Formatura",
//                         "Formatura finale dei panetti", 6, 30, true),
//                 new FaseLavorazione("Cottura in Forno",
//                         "Cottura in forno a legna a 220°C", 7, 45, true),
//                 new FaseLavorazione("Raffreddamento",
//                         "Raffreddamento su griglie", 8, 60, true)
//         };

//         // Input per le fasi
//         InputLavorazione inputGrano = new InputLavorazione(grano, 10.0, "kg");
//         InputLavorazione inputLievito = new InputLavorazione(lievito, 1.0, "kg");
//         InputLavorazione inputAcqua = new InputLavorazione(acqua, 7.0, "litri");
//         InputLavorazione inputSalePane = new InputLavorazione(salePane, 0.2, "kg");

//         service.aggiungiInputAFase(fasiPane[1], inputGrano);
//         service.aggiungiInputAFase(fasiPane[1], inputLievito);
//         service.aggiungiInputAFase(fasiPane[1], inputAcqua);
//         service.aggiungiInputAFase(fasiPane[1], inputSalePane);

//         // Aggiunge fasi
//         for (FaseLavorazione fase : fasiPane) {
//             service.aggiungiFaseAlProcesso(processoPane.getId(), fase);
//         }

//         // Prodotto finale
//         Prodotto paneTimilia = new Prodotto(11, "Pane di Timilia",
//                 "Pane tradizionale siciliano con farina di timilia",
//                 4.50, 0, panificio, processoPane.getId());

//         panificio.aggiungiProdottoTrasformato(paneTimilia, processoPane.getId());

//         ProcessoTrasformazione processoPaneCompleto = service.findById(processoPane.getId()).get();

//         System.out.println("\n📊 RISULTATI PROCESSO:");
//         System.out.println("   ⏱️  Durata totale: "
//                 + String.format("%.1f", processoPaneCompleto.getDurataTotaleStimataOre()) + " ore");
//         System.out.println(
//                 "   💰 Costo materie prime: €" + String.format("%.2f", processoPaneCompleto.getCostoTotaleStimato()));
//         System.out.println(
//                 "   🍞 Prodotto finale: " + paneTimilia.getNome() + " - €" + paneTimilia.getPrezzo() + "/pagnotta");
//         System.out.println("   🏷️  Tipo origine: " + paneTimilia.getTipoOrigine().getDescrizione());

//         System.out.println("\n" + panificio.getDescrizioneCapacita());
//     }

//     private static void mostraStatisticheSistema(IProcessoTrasformazioneService service,
//             ProcessoTrasformazioneRepository repository) {
//         System.out.println("📈 STATISTICHE SISTEMA DI TRASFORMAZIONE");
//         System.out.println("-".repeat(50));

//         List<ProcessoTrasformazione> tuttiProcessi = repository.findAll();
//         List<ProcessoTrasformazione> processiAttivi = repository.findActive();

//         System.out.println("📊 Totale processi nel sistema: " + tuttiProcessi.size());
//         System.out.println("✅ Processi attivi: " + processiAttivi.size());

//         double costeTotaliMedie = tuttiProcessi.stream()
//                 .mapToDouble(ProcessoTrasformazione::getCostoTotaleStimato)
//                 .average()
//                 .orElse(0.0);

//         double durateTotaliMedie = tuttiProcessi.stream()
//                 .mapToDouble(ProcessoTrasformazione::getDurataTotaleStimataOre)
//                 .average()
//                 .orElse(0.0);

//         System.out.println("💰 Costo medio processi: €" + String.format("%.2f", costeTotaliMedie));
//         System.out.println("⏱️  Durata media processi: " + String.format("%.1f", durateTotaliMedie) + " ore");

//         // Mostra metodi di produzione utilizzati
//         tuttiProcessi.stream()
//                 .map(ProcessoTrasformazione::getMetodoProduzione)
//                 .filter(metodo -> metodo != null && !metodo.isEmpty())
//                 .distinct()
//                 .forEach(metodo -> System.out.println("🔧 Metodo utilizzato: " + metodo));

//         System.out.println("\n✨ Sistema di trasformazione agricola operativo!");
//         System.out.println("   La tracciabilità completa è garantita dalla materia prima al prodotto finale.");
//     }
// }
