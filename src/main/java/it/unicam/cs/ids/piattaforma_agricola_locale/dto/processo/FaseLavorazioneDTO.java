package it.unicam.cs.ids.piattaforma_agricola_locale.dto.processo;

/**
 * DTO semplificato che rappresenta una fase di lavorazione all'interno di un processo.
 * Contiene informazioni dirette e testuali sulla materia prima e la sua fonte,
 * senza oggetti nidificati.
 */
public class FaseLavorazioneDTO {

    private final long id;
    private final String nome;
    private final String descrizione;
    private final int ordineEsecuzione;
    private final String materiaPrimaUtilizzata;
    private final String fonteMateriaPrima; // Descrizione testuale della fonte

    public FaseLavorazioneDTO(long id, String nome, String descrizione, int ordineEsecuzione, String materiaPrimaUtilizzata, String fonteMateriaPrima) {
        this.id = id;
        this.nome = nome;
        this.descrizione = descrizione;
        this.ordineEsecuzione = ordineEsecuzione;
        this.materiaPrimaUtilizzata = materiaPrimaUtilizzata;
        this.fonteMateriaPrima = fonteMateriaPrima;
    }

    // Getters
    public long getId() {
        return id;
    }

    public String getNome() {
        return nome;
    }

    public String getDescrizione() {
        return descrizione;
    }

    public int getOrdineEsecuzione() {
        return ordineEsecuzione;
    }

    public String getMateriaPrimaUtilizzata() {
        return materiaPrimaUtilizzata;
    }

    public String getFonteMateriaPrima() {
        return fonteMateriaPrima;
    }
}