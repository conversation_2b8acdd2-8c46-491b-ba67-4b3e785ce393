/**
 * Il package {@code dto.processo} contiene le classi Data Transfer Object (DTO)
 * per la rappresentazione dei processi di trasformazione.
 *
 * <p>
 * Questi DTO forniscono una vista semplificata e focalizzata sui dettagli
 * del processo, esponendo le informazioni in modo chiaro e testuale,
 * senza gerarchie complesse o oggetti nidificati.
 * </p>
 *
 * <h2>Gerarchia dei DTO:</h2>
 * <ul>
 * <li>{@link it.unicam.cs.ids.piattaforma_agricola_locale.dto.processo.ProcessoTrasformazioneDTO} -
 * DTO principale che rappresenta un processo di trasformazione.</li>
 * <li>{@link it.unicam.cs.ids.piattaforma_agricola_locale.dto.processo.FaseLavorazioneDTO} -
 * Dettagli di una singola fase di lavorazione, con informazioni testuali sulla fonte della materia prima.</li>
 * </ul>
 *
 * @since 1.0
 * @version 2.0
 */
package it.unicam.cs.ids.piattaforma_agricola_locale.dto.processo;