# Gestione dei Processi di Trasformazione - Documentazione Tecnica Completa

## Indice

1. [Panoramica del Sistema](#panoramica-del-sistema)
2. [Architettura e Pattern](#architettura-e-pattern)
3. [<PERSON><PERSON> di Dominio](#modello-di-dominio)
4. [Workflow Completo](#workflow-completo)
5. [Servizi e Business Logic](#servizi-e-business-logic)
6. [Layer di Repository](#layer-di-repository)
7. [DTO e Mapping](#dto-e-mapping)
8. [Casi d'Uso Pratici](#casi-duso-pratici)
9. [Test e Validazione](#test-e-validazione)
10. [Esempi di Implementazione](#esempi-di-implementazione)
11. [Best Practices](#best-practices)

---

## Panoramica del Sistema

Il sistema di gestione dei processi di trasformazione è progettato per gestire il ciclo completo di trasformazione delle materie prime in prodotti finiti all'interno della piattaforma agricola. Il sistema supporta la creazione, gestione e tracciabilità di processi articolati composti da multiple fasi sequenziali.

### Caratteristiche Principali

- **Gestione Multi-Fase**: Ogni processo può essere composto da fasi sequenziali ordinate
- **Tracciabilità delle Fonti**: Distingue tra fonti interne (produttori sulla piattaforma) ed esterne (fornitori esterni)
- **Flessibilità**: Supporta diversi metodi di produzione e tipologie di trasformazione
- **Validazione Business**: Controlla consistenza e validità dei dati inseriti
- **Pattern Repository**: Separazione netta tra logica business e persistenza dati
- **DTO Pattern**: Interfacce sicure per l'esposizione dei dati

---

## Architettura e Pattern

Il sistema segue un'architettura a strati (layered architecture) con separazione delle responsabilità:

```
┌─────────────────────────────────────┐
│           Presentation Layer        │
│        (DTO, Controllers)           │
├─────────────────────────────────────┤
│           Service Layer             │
│     (Business Logic, Mapping)       │
├─────────────────────────────────────┤
│           Repository Layer          │
│        (Data Access Logic)          │
├─────────────────────────────────────┤
│            Domain Model             │
│    (Entities, Value Objects)        │
└─────────────────────────────────────┘
```

### Design Patterns Utilizzati

1. **Repository Pattern**: Astrazione dell'accesso ai dati
2. **Service Layer Pattern**: Incapsulamento della logica business
3. **DTO Pattern**: Trasferimento sicuro dei dati
4. **Strategy Pattern**: Gestione diversi tipi di fonti materie prime
5. **Builder Pattern**: Costruzione complessa degli oggetti

---

## Modello di Dominio

### Entità Principali

#### 1. ProcessoTrasformazione

La classe core del sistema che rappresenta un processo completo di trasformazione.

```java
public class ProcessoTrasformazione {
    private Long id;
    private String nome;
    private String descrizione;
    private Trasformatore trasformatore;
    private List<FaseLavorazione> fasiLavorazione;
    private Prodotto prodottoFinale;
    private String metodoProduzione;
    private String note;
}
```

**Responsabilità:**

- Coordinare l'esecuzione delle fasi di lavorazione
- Mantenere i metadati del processo
- Validare la coerenza delle fasi aggiunte
- Fornire metodi di accesso sicuri alle informazioni

**Metodi Principali:**

- `aggiungiFase(FaseLavorazione fase)`: Aggiunge una fase al processo
- `rimuoviFase(FaseLavorazione fase)`: Rimuove una fase dal processo
- `getFasiLavorazione()`: Restituisce le fasi ordinate per esecuzione
- `getNumeroFasi()`: Conta il numero totale di fasi

#### 2. FaseLavorazione

Rappresenta una singola fase all'interno di un processo di trasformazione.

```java
public class FaseLavorazione {
    private String nome;
    private String descrizione;
    private int ordineEsecuzione;
    private String materiaPrimaUtilizzata;
    private FonteMateriaPrima fonteMateriaPrima;
}
```

**Caratteristiche:**

- **Ordinamento**: Ogni fase ha un ordine di esecuzione definito
- **Tracciabilità**: Specifica quale materia prima viene utilizzata e da dove proviene
- **Flessibilità**: Può rappresentare qualsiasi tipo di lavorazione

#### 3. Sistema di Fonti

Il sistema distingue tra due tipi di fonti per le materie prime:

##### FonteInterna

```java
public class FonteInterna implements FonteMateriaPrima {
    private Produttore produttore;
    
    @Override
    public String getNomeFornitore() {
        return produttore.getNome() + " " + produttore.getCognome() + 
               " - " + produttore.getDatiAzienda().getNomeAzienda();
    }
}
```

##### FonteEsterna

```java
public class FonteEsterna implements FonteMateriaPrima {
    private String nomeFornitore;
    
    @Override
    public String getNomeFornitore() {
        return nomeFornitore;
    }
}
```

---

## Workflow Completo

### 1. Fase di Setup

1. **Creazione Trasformatore**: Registrazione dell'azienda di trasformazione
2. **Definizione Prodotti Base**: Catalogazione delle materie prime disponibili
3. **Configurazione Fornitori**: Setup di produttori interni e fornitori esterni

### 2. Creazione Processo

```java
// Esempio di creazione processo
ProcessoTrasformazione processo = service.creaProcesso(
    "Produzione Mozzarella di Bufala",
    "Processo tradizionale siciliano",
    trasformatore,
    "Metodo Tradizionale DOP"
);
```

### 3. Configurazione Fasi

```java
// Configurazione fasi sequenziali
FaseLavorazione controlloQualita = new FaseLavorazione(
    "Controllo Qualità Latte",
    "Verifica acidità, temperatura, contaminanti",
    1, // ordine esecuzione
    "Latte di Bufala",
    new FonteInterna(produttoreLatte)
);

FaseLavorazione riscaldamento = new FaseLavorazione(
    "Riscaldamento Controllato",
    "Riscaldamento graduale a 37°C",
    2,
    "Latte di Bufala",
    new FonteInterna(produttoreLatte)
);

// Aggiunta fasi al processo
service.aggiungiFaseAlProcesso(processo.getId(), controlloQualita);
service.aggiungiFaseAlProcesso(processo.getId(), riscaldamento);
```

### 4. Creazione Prodotto Finale

```java
Prodotto mozzarella = new Prodotto(
    "Mozzarella di Bufala Siciliana DOP",
    "Prodotta con metodo tradizionale",
    15.80,
    0,
    trasformatore,
    processo.getId()
);
```

---

## Servizi e Business Logic

### IProcessoTrasformazioneService

Interfaccia che definisce le operazioni business del sistema:

```java
public interface IProcessoTrasformazioneService {
    ProcessoTrasformazione creaProcesso(String nome, String descrizione, 
                                       Trasformatore trasformatore, String metodoProduzione);
    ProcessoTrasformazione aggiornaProcesso(ProcessoTrasformazione processo);
    ProcessoTrasformazione aggiungiFaseAlProcesso(Long processoId, FaseLavorazione fase);
    ProcessoTrasformazione rimuoviFaseDalProcesso(Long processoId, FaseLavorazione fase);
    boolean eliminaProcesso(Long processoId, Trasformatore trasformatore);
}
```

### ProcessoTrasformazioneService

Implementazione concreta della logica business:

#### Creazione Processo

```java
@Override
public ProcessoTrasformazione creaProcesso(String nome, String descrizione,
        Trasformatore trasformatore, String metodoProduzione) {
    
    // Validazione input
    validaParametriCreazione(nome, descrizione, trasformatore);
    
    // Verifica unicità nome per trasformatore
    if (processoRepository.existsByNomeAndTrasformatore(nome.trim(), trasformatore)) {
        throw new IllegalArgumentException("Processo già esistente");
    }
    
    // Creazione e salvataggio
    ProcessoTrasformazione nuovoProcesso = new ProcessoTrasformazione(
        nome.trim(), descrizione.trim(), trasformatore);
    
    if (metodoProduzione != null && !metodoProduzione.trim().isEmpty()) {
        nuovoProcesso.setMetodoProduzione(metodoProduzione.trim());
    }
    
    return processoRepository.save(nuovoProcesso);
}
```

#### Gestione Fasi

```java
@Override
public ProcessoTrasformazione aggiungiFaseAlProcesso(Long processoId, FaseLavorazione fase) {
    validaParametri(processoId, fase);
    
    ProcessoTrasformazione processo = trovaProcesso(processoId);
    processo.aggiungiFase(fase);
    
    return processoRepository.save(processo);
}
```

#### Validazioni Business

- **Unicità nome processo per trasformatore**
- **Validazione parametri non nulli**
- **Controllo esistenza processo prima delle modifiche**
- **Autorizzazione trasformatore per operazioni sensitive**

---

## Layer di Repository

### IProcessoTrasformazioneRepository

Interfaccia di accesso ai dati:

```java
public interface IProcessoTrasformazioneRepository {
    ProcessoTrasformazione save(ProcessoTrasformazione processo);
    Optional<ProcessoTrasformazione> findById(Long id);
    List<ProcessoTrasformazione> findByTrasformatore(Trasformatore trasformatore);
    boolean existsByNomeAndTrasformatore(String nome, Trasformatore trasformatore);
    boolean deleteById(Long id);
    List<ProcessoTrasformazione> findAll();
}
```

### ProcessoTrasformazioneRepository

Implementazione concreta che gestisce:

- **Persistenza in memoria**: Per testing e prototipazione
- **Generazione ID**: Gestione automatica degli identificatori
- **Ricerca ottimizzata**: Implementazioni efficaci per query comuni
- **Gestione transazioni**: Consistenza delle operazioni

---

## DTO e Mapping

### ProcessoTrasformazioneDTO

DTO per l'esposizione sicura dei dati:

```java
public class ProcessoTrasformazioneDTO {
    private final long idProcesso;
    private final String nomeProcesso;
    private final String descrizioneProcesso;
    private final LocalDateTime dataInizioProcesso;
    private final LocalDateTime dataFineProcesso;
    private final String noteTecniche;
    private final int idTrasformatore;
    private final String nomeTrasformatore;
    private final String cognomeTrasformatore;
    private final String aziendaTrasformatore;
    private final List<FaseLavorazioneDTO> fasi;
}
```

### FaseLavorazioneDTO

DTO semplificato per le fasi:

```java
public class FaseLavorazioneDTO {
    private final String nome;
    private final String descrizione;
    private final int ordineEsecuzione;
    private final String materiaPrimaUtilizzata;
    private final String fonteMateriaPrima;
}
```

### ProcessoMapper

Servizio di mapping tra entità e DTO:

```java
public class ProcessoMapper {
    public ProcessoTrasformazioneDTO toDto(ProcessoTrasformazione processo) {
        if (processo == null) return null;
        
        Trasformatore trasformatore = processo.getTrasformatore();
        DatiAzienda datiAzienda = trasformatore.getDatiAzienda();
        
        List<FaseLavorazioneDTO> fasiDto = processo.getFasiLavorazione().stream()
            .map(this::toFaseDto)
            .collect(Collectors.toList());
        
        return new ProcessoTrasformazioneDTO(
            processo.getId(),
            processo.getNome(),
            processo.getDescrizione(),
            null, // dataInizio placeholder
            null, // dataFine placeholder
            processo.getNote(),
            trasformatore.getId(),
            trasformatore.getNome(),
            trasformatore.getCognome(),
            datiAzienda.getNomeAzienda(),
            fasiDto
        );
    }
    
    private FaseLavorazioneDTO toFaseDto(FaseLavorazione fase) {
        return new FaseLavorazioneDTO(
            fase.getNome(),
            fase.getDescrizione(),
            fase.getOrdineEsecuzione(),
            fase.getMateriaPrimaUtilizzata(),
            fase.getFonteMateriaPrima().getNomeFornitore()
        );
    }
}
```

---

