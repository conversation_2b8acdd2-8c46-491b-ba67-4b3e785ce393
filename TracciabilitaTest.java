import it.unicam.cs.ids.piattaforma_agricola_locale.dto.tracciabilita.TracciabilitaProdottoDTO;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.catalogo.Prodotto;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.catalogo.TipoOrigineProdotto;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.repository.ProdottoRepository;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.DatiAzienda;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.Venditore;
import it.unicam.cs.ids.piattaforma_agricola_locale.service.impl.ProdottoService;

/**
 * Test semplice per verificare il funzionamento del sistema di tracciabilità.
 */
public class TracciabilitaTest {
    
    public static void main(String[] args) {
        System.out.println("🔍 Test sistema di tracciabilità");
        
        // Creazione dati di test
        DatiAzienda datiAzienda = new DatiAzienda(
            "Azienda Agricola Siciliana", 
            "12345678901", 
            "Via Roma 123, Palermo"
        );
        
        Venditore venditore = new Venditore(
            1, 
            "Mario", 
            "Rossi", 
            "<EMAIL>", 
            "3331234567", 
            datiAzienda
        );
        
        // Prodotto materia prima
        Prodotto prodottoMateriaPrima = new Prodotto(
            101, 
            "Pomodori San Marzano", 
            "Pomodori biologici coltivati in Campania", 
            3.50, 
            100, 
            venditore
        );
        
        // Test del servizio
        ProdottoService prodottoService = new ProdottoService(new ProdottoRepository());
        
        try {
            // Salva il prodotto nel repository
            prodottoService.getProdottoRepository().save(prodottoMateriaPrima);
            
            // Test tracciabilità per materia prima
            TracciabilitaProdottoDTO tracciabilita = prodottoService.getDettagliTracciabilita(101);
            
            System.out.println("✅ Test completato con successo!");
            System.out.println("📦 Prodotto: " + tracciabilita.getNomeProdotto());
            System.out.println("👨‍🌾 Venditore: " + tracciabilita.getNomeVenditoreCorrente());
            System.out.println("🏢 Azienda: " + tracciabilita.getAziendaVenditoreCorrente());
            
            if (tracciabilita.getProduttoreOriginario() != null) {
                System.out.println("🌱 Produttore originario: " + 
                    tracciabilita.getProduttoreOriginario().getNomeProduttore() + " " +
                    tracciabilita.getProduttoreOriginario().getCognomeProduttore());
            }
            
        } catch (Exception e) {
            System.err.println("❌ Errore durante il test: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("🏁 Test terminato");
    }
}
